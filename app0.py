import streamlit as st
import pandas as pd
import json
import datetime
import uuid
from typing import Dict, Any, List
import plotly.express as px
import plotly.graph_objects as go
import numpy as np
import time
import re
from functools import lru_cache
import sqlite3
from sqlite3 import Error
import os
import threading
import logging
from contextlib import contextmanager

# Enhanced Data Structures for DSM-5 Substance Categories and Psychiatric Medications

# DSM-5 Substance Categories with specific substances and assessment fields
DSM5_SUBSTANCE_CATEGORIES = {
    "Alcohol": {
        "substances": ["Beer", "Wine", "Spirits", "Other alcoholic beverages"],
        "assessment_fields": ["frequency", "quantity", "binge_episodes", "withdrawal", "tolerance"],
        "dsm5_criteria": [
            "Tolerance", "Withdrawal", "Using more than intended", "Unsuccessful quit attempts",
            "Time spent obtaining/using", "Social/occupational problems", "Continued use despite problems",
            "Important activities given up", "Use in hazardous situations", "Continued use despite physical/psychological problems",
            "Craving or strong desire to use"
        ]
    },
    "Cannabis": {
        "substances": ["Marijuana", "Hash", "Edibles", "Concentrates", "Synthetic cannabis (K2/Spice)"],
        "assessment_fields": ["frequency", "method_use", "potency", "withdrawal", "tolerance"],
        "dsm5_criteria": [
            "Tolerance", "Withdrawal", "Using more than intended", "Unsuccessful quit attempts",
            "Time spent obtaining/using", "Social/occupational problems", "Continued use despite problems",
            "Important activities given up", "Use in hazardous situations", "Continued use despite physical/psychological problems",
            "Craving or strong desire to use"
        ]
    },
    "Stimulants": {
        "substances": ["Cocaine", "Crack cocaine", "Methamphetamine", "Amphetamines", "ADHD medications (misused)", "Synthetic stimulants"],
        "assessment_fields": ["frequency", "route", "withdrawal", "tolerance", "psychotic_symptoms"],
        "dsm5_criteria": [
            "Tolerance", "Withdrawal", "Using more than intended", "Unsuccessful quit attempts",
            "Time spent obtaining/using", "Social/occupational problems", "Continued use despite problems",
            "Important activities given up", "Use in hazardous situations", "Continued use despite physical/psychological problems",
            "Craving or strong desire to use"
        ]
    },
    "Opioids": {
        "substances": ["Heroin", "Prescription opioids", "Fentanyl", "Oxycodone", "Hydrocodone", "Morphine", "Synthetic opioids"],
        "assessment_fields": ["frequency", "route", "withdrawal", "tolerance", "overdose_history"],
        "dsm5_criteria": [
            "Tolerance", "Withdrawal", "Using more than intended", "Unsuccessful quit attempts",
            "Time spent obtaining/using", "Social/occupational problems", "Continued use despite problems",
            "Important activities given up", "Use in hazardous situations", "Continued use despite physical/psychological problems",
            "Craving or strong desire to use"
        ]
    },
    "Sedatives_Hypnotics_Anxiolytics": {
        "substances": ["Benzodiazepines", "Barbiturates", "Sleep medications", "Muscle relaxants"],
        "assessment_fields": ["frequency", "prescribed_vs_illicit", "withdrawal", "tolerance"],
        "dsm5_criteria": [
            "Tolerance", "Withdrawal", "Using more than intended", "Unsuccessful quit attempts",
            "Time spent obtaining/using", "Social/occupational problems", "Continued use despite problems",
            "Important activities given up", "Use in hazardous situations", "Continued use despite physical/psychological problems",
            "Craving or strong desire to use"
        ]
    },
    "Hallucinogens": {
        "substances": ["LSD", "Psilocybin mushrooms", "PCP", "MDMA/Ecstasy", "Ketamine", "Synthetic hallucinogens"],
        "assessment_fields": ["frequency", "bad_trips", "flashbacks", "tolerance"],
        "dsm5_criteria": [
            "Tolerance", "Using more than intended", "Unsuccessful quit attempts",
            "Time spent obtaining/using", "Social/occupational problems", "Continued use despite problems",
            "Important activities given up", "Use in hazardous situations", "Continued use despite physical/psychological problems",
            "Craving or strong desire to use", "Flashbacks or persistent perceptual disturbances"
        ]
    },
    "Inhalants": {
        "substances": ["Solvents", "Aerosols", "Gases", "Nitrites"],
        "assessment_fields": ["frequency", "method", "medical_complications"],
        "dsm5_criteria": [
            "Using more than intended", "Unsuccessful quit attempts",
            "Time spent obtaining/using", "Social/occupational problems", "Continued use despite problems",
            "Important activities given up", "Use in hazardous situations", "Continued use despite physical/psychological problems",
            "Craving or strong desire to use"
        ]
    },
    "Tobacco": {
        "substances": ["Cigarettes", "Cigars", "Pipe tobacco", "Chewing tobacco", "E-cigarettes/Vaping", "Nicotine patches/gum"],
        "assessment_fields": ["frequency", "quantity", "quit_attempts", "withdrawal"],
        "dsm5_criteria": [
            "Tolerance", "Withdrawal", "Using more than intended", "Unsuccessful quit attempts",
            "Time spent obtaining/using", "Social/occupational problems", "Continued use despite problems",
            "Important activities given up", "Continued use despite physical/psychological problems",
            "Craving or strong desire to use"
        ]
    },
    "Caffeine": {
        "substances": ["Coffee", "Tea", "Energy drinks", "Caffeine pills", "Soft drinks"],
        "assessment_fields": ["daily_intake", "withdrawal_symptoms", "sleep_impact"],
        "dsm5_criteria": [
            "Tolerance", "Withdrawal", "Using more than intended", "Unsuccessful quit attempts",
            "Continued use despite physical/psychological problems", "Craving or strong desire to use"
        ]
    },
    "Other_Unknown": {
        "substances": ["Synthetic drugs", "Unknown substances", "Other"],
        "assessment_fields": ["frequency", "effects", "complications"],
        "dsm5_criteria": [
            "Tolerance", "Withdrawal", "Using more than intended", "Unsuccessful quit attempts",
            "Time spent obtaining/using", "Social/occupational problems", "Continued use despite problems",
            "Important activities given up", "Use in hazardous situations", "Continued use despite physical/psychological problems",
            "Craving or strong desire to use"
        ]
    }
}

# Comprehensive Psychiatric Medication Classes
PSYCHIATRIC_MEDICATION_CLASSES = {
    "Antidepressants": {
        "SSRIs": ["Sertraline (Zoloft)", "Fluoxetine (Prozac)", "Escitalopram (Lexapro)", "Paroxetine (Paxil)", "Citalopram (Celexa)", "Fluvoxamine (Luvox)"],
        "SNRIs": ["Venlafaxine (Effexor)", "Duloxetine (Cymbalta)", "Desvenlafaxine (Pristiq)", "Levomilnacipran (Fetzima)"],
        "Atypical": ["Bupropion (Wellbutrin)", "Mirtazapine (Remeron)", "Trazodone", "Vilazodone (Viibryd)", "Vortioxetine (Trintellix)"],
        "TCAs": ["Amitriptyline", "Nortriptyline", "Imipramine", "Desipramine", "Clomipramine", "Doxepin"],
        "MAOIs": ["Phenelzine (Nardil)", "Tranylcypromine (Parnate)", "Selegiline (Emsam)", "Isocarboxazid (Marplan)"]
    },
    "Mood_Stabilizers": {
        "Lithium": ["Lithium carbonate", "Lithium citrate"],
        "Anticonvulsants": ["Valproic acid (Depakote)", "Carbamazepine (Tegretol)", "Lamotrigine (Lamictal)", "Oxcarbazepine (Trileptal)", "Topiramate (Topamax)", "Gabapentin"]
    },
    "Antipsychotics": {
        "Atypical": ["Risperidone (Risperdal)", "Olanzapine (Zyprexa)", "Quetiapine (Seroquel)", "Aripiprazole (Abilify)", "Ziprasidone (Geodon)", "Paliperidone (Invega)", "Clozapine (Clozaril)", "Asenapine (Saphris)", "Lurasidone (Latuda)", "Brexpiprazole (Rexulti)"],
        "Typical": ["Haloperidol (Haldol)", "Chlorpromazine (Thorazine)", "Fluphenazine (Prolixin)", "Perphenazine", "Thiothixene (Navane)", "Trifluoperazine (Stelazine)"]
    },
    "Anxiolytics_Benzodiazepines": {
        "Short_acting": ["Alprazolam (Xanax)", "Lorazepam (Ativan)", "Triazolam (Halcion)", "Oxazepam (Serax)"],
        "Long_acting": ["Clonazepam (Klonopin)", "Diazepam (Valium)", "Chlordiazepoxide (Librium)", "Clorazepate (Tranxene)"]
    },
    "Stimulants": {
        "ADHD_medications": ["Methylphenidate (Ritalin)", "Amphetamine (Adderall)", "Lisdexamfetamine (Vyvanse)", "Atomoxetine (Strattera)", "Dextroamphetamine (Dexedrine)", "Guanfacine (Intuniv)", "Clonidine (Kapvay)"]
    },
    "Sleep_Medications": {
        "Z_drugs": ["Zolpidem (Ambien)", "Eszopiclone (Lunesta)", "Zaleplon (Sonata)"],
        "Other": ["Ramelteon (Rozerem)", "Suvorexant (Belsomra)", "Doxepin (Silenor)", "Melatonin"]
    },
    "Anti_anxiety_Non_Benzo": {
        "medications": ["Buspirone (Buspar)", "Hydroxyzine (Vistaril)", "Gabapentin", "Pregabalin (Lyrica)", "Propranolol"]
    },
    "Other_Psychiatric": {
        "medications": ["Memantine (Namenda)", "Donepezil (Aricept)", "Modafinil (Provigil)", "Naltrexone", "Acamprosate (Campral)", "Disulfiram (Antabuse)", "Varenicline (Chantix)"]
    }
}

# Data validation functions
def validate_substance_use_data(substance_data: Dict[str, Any]) -> List[str]:
    """Validate substance use data for consistency and completeness"""
    errors = []
    
    if not isinstance(substance_data, dict):
        errors.append("Substance data must be a dictionary")
        return errors
    
    # Check for logical consistency
    for category, data in substance_data.items():
        if category in DSM5_SUBSTANCE_CATEGORIES and isinstance(data, dict):
            # Can't have withdrawal without use
            if data.get('withdrawal') and not data.get('used'):
                errors.append(f"{category}: Cannot have withdrawal without substance use")
            
            # Can't have tolerance without use
            if data.get('tolerance') and not data.get('used'):
                errors.append(f"{category}: Cannot have tolerance without substance use")
            
            # Age onset should be reasonable
            if data.get('age_onset') and (data.get('age_onset') < 5 or data.get('age_onset') > 80):
                errors.append(f"{category}: Age of onset should be between 5 and 80")
            
            # Validate DSM-5 criteria count
            if 'dsm5_criteria' in data and isinstance(data['dsm5_criteria'], list):
                criteria_count = len([c for c in data['dsm5_criteria'] if c])
                if criteria_count > 11:  # Maximum DSM-5 criteria
                    errors.append(f"{category}: Too many DSM-5 criteria selected ({criteria_count}/11)")
    
    return errors

def validate_medication_history_data(medication_data: Dict[str, Any]) -> List[str]:
    """Validate medication history data for consistency and completeness"""
    errors = []
    
    if not isinstance(medication_data, dict):
        errors.append("Medication data must be a dictionary")
        return errors
    
    for med_class, data in medication_data.items():
        if isinstance(data, dict) and 'trials' in data:
            for i, trial in enumerate(data['trials']):
                if isinstance(trial, dict):
                    # Validate response rating
                    if 'response_rating' in trial and trial['response_rating']:
                        if not (1 <= trial['response_rating'] <= 5):
                            errors.append(f"{med_class} trial {i+1}: Response rating must be between 1 and 5")
                    
                    # Validate adherence rating
                    if 'adherence_rating' in trial and trial['adherence_rating']:
                        if not (1 <= trial['adherence_rating'] <= 5):
                            errors.append(f"{med_class} trial {i+1}: Adherence rating must be between 1 and 5")
                    
                    # Validate duration
                    if 'duration_weeks' in trial and trial['duration_weeks']:
                        if trial['duration_weeks'] < 0:
                            errors.append(f"{med_class} trial {i+1}: Duration cannot be negative")
                        elif trial['duration_weeks'] > 520:  # 10 years
                            errors.append(f"{med_class} trial {i+1}: Duration seems unreasonably long (>10 years)")
                    
                    # Validate medication name is not empty
                    if 'medication' in trial and not trial['medication'].strip():
                        errors.append(f"{med_class} trial {i+1}: Medication name cannot be empty")
    
    return errors

def get_dsm5_severity(criteria_count: int) -> str:
    """Calculate DSM-5 substance use disorder severity based on criteria count"""
    if criteria_count >= 6:
        return "Severe"
    elif criteria_count >= 4:
        return "Moderate"
    elif criteria_count >= 2:
        return "Mild"
    else:
        return "No disorder"

def migrate_existing_substance_data(old_data: Dict[str, Any]) -> Dict[str, Any]:
    """Migrate existing substance use data to new DSM-5 structure"""
    new_data = {}
    
    # Preserve existing alcohol data
    if 'alcohol' in old_data:
        new_data['Alcohol'] = old_data['alcohol']
    
    # Migrate drug categories to new DSM-5 categories
    if 'drugs' in old_data and 'substances_used' in old_data['drugs']:
        substances_used = old_data['drugs']['substances_used']
        
        # Map old substances to new categories
        for substance in substances_used:
            if substance in ["Marijuana", "Hash", "Edibles", "Concentrates"]:
                if 'Cannabis' not in new_data:
                    new_data['Cannabis'] = {'used': True, 'substances': []}
                new_data['Cannabis']['substances'].append(substance)
            elif substance in ["Cocaine", "Crack cocaine", "Methamphetamine", "Amphetamines"]:
                if 'Stimulants' not in new_data:
                    new_data['Stimulants'] = {'used': True, 'substances': []}
                new_data['Stimulants']['substances'].append(substance)
            # Add more mappings as needed
    
    # Preserve tobacco data
    if 'tobacco' in old_data:
        new_data['Tobacco'] = old_data['tobacco']
    
    # Preserve caffeine data
    if 'caffeine' in old_data:
        new_data['Caffeine'] = old_data['caffeine']
    
    return new_data

def migrate_existing_medication_data(old_data: Dict[str, Any]) -> Dict[str, Any]:
    """Migrate existing medication history data to new comprehensive structure"""
    new_data = {}
    
    # Migrate existing antidepressants
    if 'antidepressants_tried' in old_data:
        new_data['Antidepressants'] = {
            'trials': [],
            'overall_class_response': old_data['antidepressants_tried'].get('response', '')
        }
        
        # Convert old format to new trial format
        if 'medications' in old_data['antidepressants_tried']:
            for med in old_data['antidepressants_tried']['medications']:
                trial = {
                    'medication': med,
                    'response_rating': None,
                    'side_effects': [],
                    'discontinuation_reason': '',
                    'adherence_rating': None
                }
                new_data['Antidepressants']['trials'].append(trial)
    
    # Migrate existing mood stabilizers
    if 'mood_stabilizers_tried' in old_data:
        new_data['Mood_Stabilizers'] = {
            'trials': [],
            'overall_class_response': old_data['mood_stabilizers_tried'].get('response', '')
        }
        
        if 'medications' in old_data['mood_stabilizers_tried']:
            for med in old_data['mood_stabilizers_tried']['medications']:
                trial = {
                    'medication': med,
                    'response_rating': None,
                    'side_effects': [],
                    'discontinuation_reason': '',
                    'adherence_rating': None
                }
                new_data['Mood_Stabilizers']['trials'].append(trial)
    
    # Migrate existing antipsychotics
    if 'antipsychotics_tried' in old_data:
        new_data['Antipsychotics'] = {
            'trials': [],
            'overall_class_response': old_data['antipsychotics_tried'].get('response', '')
        }
        
        if 'medications' in old_data['antipsychotics_tried']:
            for med in old_data['antipsychotics_tried']['medications']:
                trial = {
                    'medication': med,
                    'response_rating': None,
                    'side_effects': [],
                    'discontinuation_reason': '',
                    'adherence_rating': None
                }
                new_data['Antipsychotics']['trials'].append(trial)
    
    return new_data

def recover_corrupted_data(data_type: str = "all") -> Dict[str, Any]:
    """Attempt to recover corrupted or missing data from various sources"""
    recovered_data = {}
    
    try:
        # Try to recover from database
        if data_type in ["all", "substance"]:
            # Attempt to get last known good substance data
            with db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT substance_use_data FROM assessments 
                    WHERE patient_id = ? AND substance_use_data IS NOT NULL 
                    ORDER BY created_at DESC LIMIT 1
                """, (st.session_state.get('patient_id', ''),))
                
                result = cursor.fetchone()
                if result and result[0]:
                    try:
                        substance_data = json.loads(result[0])
                        recovered_data['substance_use'] = substance_data
                    except json.JSONDecodeError:
                        pass
        
        if data_type in ["all", "medication"]:
            # Attempt to get last known good medication data
            with db_manager.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT medication_history FROM assessments 
                    WHERE patient_id = ? AND medication_history IS NOT NULL 
                    ORDER BY created_at DESC LIMIT 1
                """, (st.session_state.get('patient_id', ''),))
                
                result = cursor.fetchone()
                if result and result[0]:
                    try:
                        medication_data = json.loads(result[0])
                        recovered_data['medication_history'] = medication_data
                    except json.JSONDecodeError:
                        pass
        
        # If no database recovery, create minimal valid structure
        if data_type in ["all", "substance"] and 'substance_use' not in recovered_data:
            recovered_data['substance_use'] = {
                category: {'used': False} for category in DSM5_SUBSTANCE_CATEGORIES.keys()
            }
        
        if data_type in ["all", "medication"] and 'medication_history' not in recovered_data:
            recovered_data['medication_history'] = {
                med_class: {'trials': [], 'overall_class_response': ''} 
                for med_class in PSYCHIATRIC_MEDICATION_CLASSES.keys()
            }
        
    except Exception as e:
        st.error(f"Data recovery failed: {e}")
        # Return minimal valid structure as fallback
        if data_type in ["all", "substance"]:
            recovered_data['substance_use'] = {
                category: {'used': False} for category in DSM5_SUBSTANCE_CATEGORIES.keys()
            }
        if data_type in ["all", "medication"]:
            recovered_data['medication_history'] = {
                med_class: {'trials': [], 'overall_class_response': ''} 
                for med_class in PSYCHIATRIC_MEDICATION_CLASSES.keys()
            }
    
    return recovered_data

def validate_dsm5_criteria_consistency(substance_data: Dict[str, Any]) -> List[str]:
    """Validate DSM-5 criteria consistency across substance categories"""
    errors = []
    
    if not isinstance(substance_data, dict):
        return errors
    
    substances = substance_data.get('substances', {})
    if not isinstance(substances, dict):
        return errors
    
    for category, data in substances.items():
        if category in DSM5_SUBSTANCE_CATEGORIES and isinstance(data, dict):
            if data.get('used'):
                # Check if DSM-5 criteria are properly structured
                dsm5_criteria = data.get('dsm5_criteria', [])
                if isinstance(dsm5_criteria, list):
                    criteria_count = len([c for c in dsm5_criteria if c])
                    
                    # Validate severity assessment matches criteria count
                    severity = get_dsm5_severity(criteria_count)
                    if 'severity_assessment' in data and data['severity_assessment']:
                        if data['severity_assessment'] != severity:
                            errors.append(f"{category}: Severity assessment '{data['severity_assessment']}' doesn't match DSM-5 criteria count ({criteria_count})")
                    
                    # Check for logical inconsistencies
                    if criteria_count >= 2 and not data.get('problems_reported'):
                        errors.append(f"{category}: DSM-5 criteria met but no problems reported")
                    
                    if criteria_count == 0 and data.get('problems_reported'):
                        errors.append(f"{category}: Problems reported but no DSM-5 criteria met")
    
    return errors

def validate_medication_trial_consistency(medication_data: Dict[str, Any]) -> List[str]:
    """Validate medication trial data for logical consistency"""
    errors = []
    
    if not isinstance(medication_data, dict):
        return errors
    
    for med_class, data in medication_data.items():
        if isinstance(data, dict) and 'trials' in data:
            trials = data.get('trials', [])
            if isinstance(trials, list):
                for i, trial in enumerate(trials):
                    if isinstance(trial, dict):
                        # Check response vs discontinuation reason consistency
                        response = trial.get('response_rating')
                        discontinuation = trial.get('discontinuation_reason', '')
                        
                        if response and response >= 4 and 'ineffective' in discontinuation.lower():
                            errors.append(f"{med_class} trial {i+1}: High response rating but discontinued for ineffectiveness")
                        
                        if response and response <= 2 and 'effective' in discontinuation.lower():
                            errors.append(f"{med_class} trial {i+1}: Low response rating but discontinued because it was effective")
                        
                        # Check adherence vs side effects
                        adherence = trial.get('adherence_rating')
                        side_effects = trial.get('side_effects', [])
                        
                        if adherence and adherence <= 2 and not side_effects:
                            errors.append(f"{med_class} trial {i+1}: Poor adherence but no side effects reported")
                        
                        # Check duration reasonableness
                        duration = trial.get('duration_weeks')
                        if duration and duration > 0:
                            if duration < 2 and response and response >= 3:
                                errors.append(f"{med_class} trial {i+1}: Very short trial duration ({duration} weeks) for reported good response")
    
    return errors

# Enhanced UI/UX Helper Functions for Progressive Disclosure and Better Visual Hierarchy

def create_smart_disclosure_section(title, content_func, key_prefix, expanded=False, help_text=None, priority="normal"):
    """Create an intelligent progressive disclosure section with smart expansion logic"""
    # Determine if section should be auto-expanded based on data
    auto_expand = expanded or st.session_state.get(f"{key_prefix}_has_data", False)
    
    # Add priority indicator
    priority_icon = "🔥" if priority == "high" else "⭐" if priority == "medium" else "📋"
    enhanced_title = f"{priority_icon} {title}"
    
    with st.expander(enhanced_title, expanded=auto_expand):
        if help_text:
            st.info(f"💡 {help_text}")
        
        # Add completion indicator
        completion_status = st.session_state.get(f"{key_prefix}_completion", 0)
        if completion_status > 0:
            st.progress(completion_status / 100, text=f"Section {completion_status}% complete")
        
        content_func()

def render_smart_navigation_aids(data, section_type):
    """Render smart navigation aids based on current data state"""
    if section_type == "substance":
        substances_with_data = [k for k, v in data.get('substances', {}).items() if v.get('used')]
        if substances_with_data:
            st.markdown("""
            <div class="navigation-aid">
                <span class="icon">🎯</span>
                <strong>Quick Navigation:</strong> You have data for: """ + ", ".join(substances_with_data) + """
            </div>
            """, unsafe_allow_html=True)
    
    elif section_type == "medication":
        classes_with_trials = [k for k, v in data.items() if isinstance(v, dict) and v.get('trials')]
        if classes_with_trials:
            st.markdown("""
            <div class="navigation-aid">
                <span class="icon">💊</span>
                <strong>Medication History:</strong> """ + f"{len(classes_with_trials)} classes with trial data" + """
            </div>
            """, unsafe_allow_html=True)

def create_enhanced_tooltip(text, tooltip_content, icon="ℹ️"):
    """Create an enhanced tooltip with better styling"""
    return f"""
    <span title="{tooltip_content}" style="cursor: help; border-bottom: 1px dotted #666;">
        {icon} {text}
    </span>
    """

def render_completion_indicator(section_data, required_fields):
    """Render a completion indicator for a section"""
    completed_fields = sum(1 for field in required_fields if section_data.get(field))
    completion_percentage = (completed_fields / len(required_fields)) * 100
    
    color = "#28a745" if completion_percentage >= 80 else "#ffc107" if completion_percentage >= 50 else "#dc3545"
    
    st.markdown(f"""
    <div style="background: linear-gradient(90deg, {color} {completion_percentage}%, #e9ecef {completion_percentage}%); 
                height: 8px; border-radius: 4px; margin: 10px 0;">
    </div>
    <small style="color: {color};">Section {completion_percentage:.0f}% complete ({completed_fields}/{len(required_fields)} fields)</small>
    """, unsafe_allow_html=True)

def render_enhanced_css():
    """Render enhanced CSS for improved UI/UX with progressive disclosure and visual hierarchy"""
    st.markdown("""
    <style>
    /* Enhanced Section Headers with improved gradient */
    .section-header {
        background: linear-gradient(135deg, #1f77b4 0%, #2ca02c 50%, #17a2b8 100%);
        color: white;
        padding: 18px 25px;
        border-radius: 12px;
        font-size: 26px;
        font-weight: 700;
        margin-bottom: 25px;
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
        text-align: center;
        letter-spacing: 0.5px;
    }
    
    /* Enhanced Expander Styling with better visual hierarchy */
    .streamlit-expanderHeader {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: 2px solid #dee2e6;
        border-radius: 10px;
        padding: 12px 18px;
        font-weight: 700;
        font-size: 17px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }
    
    .streamlit-expanderHeader:hover {
        background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
        border-color: #adb5bd;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    .streamlit-expanderContent {
        border: 2px solid #dee2e6;
        border-top: none;
        border-radius: 0 0 10px 10px;
        padding: 25px;
        background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.02);
    }
    
    /* Enhanced Substance Category Icons and Colors with better visual hierarchy */
    .substance-category {
        border-left: 6px solid;
        padding: 15px 20px;
        margin: 15px 0;
        border-radius: 0 8px 8px 0;
        background: linear-gradient(90deg, rgba(255,255,255,0.9) 0%, rgba(248,249,250,0.9) 100%);
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
    }
    
    .substance-category:hover {
        transform: translateX(3px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
    }
    
    .alcohol { 
        border-left-color: #ff6b6b; 
        background: linear-gradient(90deg, rgba(255,107,107,0.05) 0%, rgba(248,249,250,0.9) 100%);
    }
    .cannabis { 
        border-left-color: #51cf66; 
        background: linear-gradient(90deg, rgba(81,207,102,0.05) 0%, rgba(248,249,250,0.9) 100%);
    }
    .stimulants { 
        border-left-color: #ffd43b; 
        background: linear-gradient(90deg, rgba(255,212,59,0.05) 0%, rgba(248,249,250,0.9) 100%);
    }
    .opioids { 
        border-left-color: #ff8cc8; 
        background: linear-gradient(90deg, rgba(255,140,200,0.05) 0%, rgba(248,249,250,0.9) 100%);
    }
    .sedatives { 
        border-left-color: #74c0fc; 
        background: linear-gradient(90deg, rgba(116,192,252,0.05) 0%, rgba(248,249,250,0.9) 100%);
    }
    .hallucinogens { 
        border-left-color: #da77f2; 
        background: linear-gradient(90deg, rgba(218,119,242,0.05) 0%, rgba(248,249,250,0.9) 100%);
    }
    .inhalants { 
        border-left-color: #95a5a6; 
        background: linear-gradient(90deg, rgba(149,165,166,0.05) 0%, rgba(248,249,250,0.9) 100%);
    }
    .tobacco { 
        border-left-color: #8b4513; 
        background: linear-gradient(90deg, rgba(139,69,19,0.05) 0%, rgba(248,249,250,0.9) 100%);
    }
    .caffeine { 
        border-left-color: #6f4e37; 
        background: linear-gradient(90deg, rgba(111,78,55,0.05) 0%, rgba(248,249,250,0.9) 100%);
    }
    .other { 
        border-left-color: #868e96; 
        background: linear-gradient(90deg, rgba(134,142,150,0.05) 0%, rgba(248,249,250,0.9) 100%);
    }
    
    /* Enhanced Medication Class Styling with better organization */
    .medication-class {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: 2px solid #dee2e6;
        border-radius: 12px;
        padding: 20px;
        margin: 15px 0;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        position: relative;
    }
    
    .medication-class:hover {
        border-color: #adb5bd;
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
        transform: translateY(-2px);
    }
    
    .medication-class::before {
        content: "💊";
        position: absolute;
        top: -10px;
        left: 15px;
        background: white;
        padding: 5px 8px;
        border-radius: 50%;
        font-size: 16px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .medication-subclass {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        margin: 12px 0;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);
        transition: all 0.2s ease;
    }
    
    .medication-subclass:hover {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-color: #dee2e6;
        transform: translateX(5px);
    }
    
    /* Enhanced DSM-5 Criteria Styling with better visual hierarchy */
    .dsm5-criteria {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        border: 2px solid #ffd700;
        border-radius: 10px;
        padding: 18px;
        margin: 15px 0;
        box-shadow: 0 3px 6px rgba(255, 215, 0, 0.2);
        position: relative;
    }
    
    .dsm5-criteria::before {
        content: "📋";
        position: absolute;
        top: -12px;
        left: 15px;
        background: white;
        padding: 6px 10px;
        border-radius: 50%;
        font-size: 18px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .dsm5-criteria h4 {
        color: #856404;
        margin-top: 5px;
        font-weight: 700;
    }
    
    .severity-indicator {
        padding: 5px 12px;
        border-radius: 20px;
        font-weight: bold;
        text-align: center;
        margin: 5px 0;
    }
    
    .severity-mild { background-color: #d4edda; color: #155724; }
    .severity-moderate { background-color: #fff3cd; color: #856404; }
    .severity-severe { background-color: #f8d7da; color: #721c24; }
    .severity-none { background-color: #e2e3e5; color: #383d41; }
    
    /* Enhanced Tooltip Styling with better accessibility */
    .tooltip-container {
        position: relative;
        display: inline-block;
        cursor: help;
        color: #007bff;
        font-weight: 600;
        text-decoration: underline dotted;
        transition: color 0.2s ease;
    }
    
    .tooltip-container:hover {
        color: #0056b3;
    }
    
    .tooltip-text {
        visibility: hidden;
        width: 320px;
        background: linear-gradient(135deg, #343a40 0%, #495057 100%);
        color: #fff;
        text-align: left;
        border-radius: 8px;
        padding: 12px 15px;
        position: absolute;
        z-index: 1000;
        bottom: 130%;
        left: 50%;
        margin-left: -160px;
        opacity: 0;
        transition: all 0.3s ease;
        font-size: 13px;
        line-height: 1.5;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        border: 1px solid #6c757d;
    }
    
    .tooltip-text::after {
        content: "";
        position: absolute;
        top: 100%;
        left: 50%;
        margin-left: -5px;
        border-width: 5px;
        border-style: solid;
        border-color: #495057 transparent transparent transparent;
    }
    
    .tooltip-container:hover .tooltip-text {
        visibility: visible;
        opacity: 1;
        transform: translateY(-5px);
    }
    
    /* Progress Indicators */
    .progress-indicator {
        display: flex;
        align-items: center;
        margin: 10px 0;
    }
    
    .progress-step {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
        font-weight: bold;
        font-size: 14px;
    }
    
    .step-completed { background-color: #28a745; color: white; }
    .step-current { background-color: #007bff; color: white; }
    .step-pending { background-color: #e9ecef; color: #6c757d; }
    
    /* Enhanced Responsive Design */
    @media (max-width: 768px) {
        .section-header {
            font-size: 20px;
            padding: 12px 15px;
            margin-bottom: 15px;
        }
        
        .medication-class, .substance-category {
            padding: 12px 15px;
            margin: 10px 0;
            border-left-width: 4px;
        }
        
        .tooltip-text {
            width: 250px;
            margin-left: -125px;
            font-size: 13px;
        }
        
        .clinical-guidance {
            padding: 12px;
            margin: 10px 0;
            font-size: 14px;
        }
        
        /* Mobile-specific improvements */
        .streamlit-expanderHeader {
            font-size: 15px;
            padding: 10px 15px;
        }
        
        .streamlit-expanderContent {
            padding: 15px;
        }
        
        /* Better touch targets for mobile */
        .help-indicator {
            font-size: 16px;
            padding: 8px;
            min-width: 32px;
            min-height: 32px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
    }
    
    /* Tablet optimizations */
    @media (min-width: 769px) and (max-width: 1024px) {
        .section-header {
            font-size: 24px;
            padding: 16px 20px;
        }
        
        .substance-category, .medication-class {
            padding: 18px 20px;
        }
        
        .tooltip-text {
            width: 300px;
            margin-left: -150px;
        }
        
        .clinical-guidance {
            padding: 16px 20px;
        }
    }
    
    /* Large screen optimizations */
    @media (min-width: 1200px) {
        .section-header {
            font-size: 28px;
            padding: 20px 30px;
        }
        
        .substance-category, .medication-class {
            padding: 25px 30px;
        }
        
        .tooltip-text {
            width: 350px;
            margin-left: -175px;
        }
        
        .clinical-guidance {
            padding: 20px 25px;
        }
    }
    
    /* Enhanced Clinical Guidance Boxes with better visual appeal */
    .clinical-guidance {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 50%, #90caf9 100%);
        border-left: 6px solid #2196f3;
        padding: 18px 22px;
        margin: 20px 0;
        border-radius: 0 12px 12px 0;
        box-shadow: 0 4px 8px rgba(33, 150, 243, 0.15);
        position: relative;
        transition: all 0.3s ease;
    }
    
    .clinical-guidance:hover {
        transform: translateX(3px);
        box-shadow: 0 6px 16px rgba(33, 150, 243, 0.2);
    }
    
    .clinical-guidance::before {
        content: "🩺";
        position: absolute;
        top: -10px;
        left: -15px;
        background: white;
        padding: 8px 10px;
        border-radius: 50%;
        font-size: 18px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        border: 2px solid #2196f3;
    }
    
    .clinical-guidance h4 {
        color: #1976d2;
        margin-top: 5px;
        margin-bottom: 10px;
        font-size: 17px;
        font-weight: 700;
    }
    
    .clinical-guidance p {
        margin-bottom: 0;
        line-height: 1.6;
        color: #0d47a1;
    }
    
    /* Enhanced Form Controls */
    .stSelectbox > div > div {
        border-radius: 6px;
    }
    
    .stMultiSelect > div > div {
        border-radius: 6px;
    }
    
    .stCheckbox > label {
        font-weight: 500;
    }
    
    /* Collapsible Sections */
    .collapsible-section {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        margin: 10px 0;
        overflow: hidden;
    }
    
    .collapsible-header {
        background-color: #f8f9fa;
        padding: 12px 15px;
        cursor: pointer;
        font-weight: 600;
        border-bottom: 1px solid #dee2e6;
    }
    
    .collapsible-content {
        padding: 15px;
        background-color: white;
    }
    
    /* Progressive Disclosure Enhancements */
    .disclosure-summary {
        background: linear-gradient(135deg, #f1f3f4 0%, #e8eaed 100%);
        border: 1px solid #dadce0;
        border-radius: 8px;
        padding: 15px;
        margin: 10px 0;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .disclosure-summary:hover {
        background: linear-gradient(135deg, #e8eaed 0%, #dadce0 100%);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    .disclosure-summary.active {
        border-color: #1976d2;
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    }
    
    .quick-access-panel {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: 2px solid #dee2e6;
        border-radius: 12px;
        padding: 20px;
        margin: 20px 0;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    }
    
    .quick-access-panel h4 {
        color: #495057;
        margin-bottom: 15px;
        font-weight: 700;
        text-align: center;
    }
    
    /* Smart Navigation Aids */
    .section-progress {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 12px 20px;
        border-radius: 25px;
        margin: 15px 0;
        border: 1px solid #dee2e6;
    }
    
    .progress-item {
        display: flex;
        align-items: center;
        font-size: 14px;
        font-weight: 600;
    }
    
    .progress-item.completed {
        color: #28a745;
    }
    
    .progress-item.current {
        color: #007bff;
    }
    
    .progress-item.pending {
        color: #6c757d;
    }
    
    /* Enhanced Form Controls with better UX */
    .enhanced-multiselect {
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 10px;
        margin: 8px 0;
        transition: border-color 0.3s ease;
    }
    
    .enhanced-multiselect:focus-within {
        border-color: #007bff;
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    }
    
    /* Contextual Help Indicators */
    .help-indicator {
        display: inline-block;
        width: 20px;
        height: 20px;
        background: #17a2b8;
        color: white;
        border-radius: 50%;
        text-align: center;
        line-height: 20px;
        font-size: 12px;
        font-weight: bold;
        margin-left: 8px;
        cursor: help;
        transition: all 0.2s ease;
    }
    
    .help-indicator:hover {
        background: #138496;
        transform: scale(1.1);
    }
    
    /* Responsive Enhancements */
    @media (max-width: 1024px) {
        .substance-category, .medication-class {
            padding: 12px 15px;
        }
        
        .tooltip-text {
            width: 280px;
            margin-left: -140px;
        }
        
        .clinical-guidance {
            padding: 15px 18px;
        }
    }
    
    @media (max-width: 768px) {
        .section-header {
            font-size: 22px;
            padding: 15px 20px;
        }
        
        .substance-category, .medication-class {
            padding: 10px 12px;
            margin: 10px 0;
        }
        
        .tooltip-text {
            width: 250px;
            margin-left: -125px;
            font-size: 12px;
        }
        
        .clinical-guidance {
            padding: 12px 15px;
        }
        
        .quick-access-panel {
            padding: 15px;
        }
        
        .section-progress {
            flex-direction: column;
            gap: 10px;
        }
    }
    
    @media (max-width: 480px) {
        .section-header {
            font-size: 20px;
            padding: 12px 15px;
        }
        
        .substance-category, .medication-class {
            padding: 8px 10px;
        }
        
        .tooltip-text {
            width: 200px;
            margin-left: -100px;
        }
        
        .clinical-guidance::before {
            display: none;
        }
        
        .medication-class::before {
            display: none;
        }
        
        .dsm5-criteria::before {
            display: none;
        }
    }
    </style>
    """, unsafe_allow_html=True)

def create_tooltip(text, tooltip_content):
    """Create an enhanced tooltip for clinical guidance"""
    return f"""
    <div class="tooltip-container">
        {text} <span class="help-indicator">?</span>
        <span class="tooltip-text">{tooltip_content}</span>
    </div>
    """

def create_enhanced_multiselect_with_guidance(label, options, default_values=None, help_text=None, clinical_guidance=None, key=None):
    """Create an enhanced multiselect with clinical guidance and better UX"""
    col1, col2 = st.columns([3, 1])
    
    with col1:
        selected = st.multiselect(
            label,
            options,
            default=default_values or [],
            help=help_text,
            key=key
        )
    
    with col2:
        if clinical_guidance:
            with st.expander("💡 Clinical Guide", expanded=False):
                st.markdown(clinical_guidance, unsafe_allow_html=True)
    
    return selected

def create_smart_selectbox_with_tooltip(label, options, default_index=0, tooltip_content=None, help_text=None, key=None):
    """Create a selectbox with enhanced tooltip and clinical guidance"""
    if tooltip_content:
        label_with_tooltip = create_tooltip(label, tooltip_content)
        st.markdown(label_with_tooltip, unsafe_allow_html=True)
        return st.selectbox("", options, index=default_index, help=help_text, key=key, label_visibility="collapsed")
    else:
        return st.selectbox(label, options, index=default_index, help=help_text, key=key)

def create_collapsible_info_panel(title, content, icon="💡", expanded=False):
    """Create a collapsible information panel for clinical guidance"""
    with st.expander(f"{icon} {title}", expanded=expanded):
        st.markdown(content, unsafe_allow_html=True)

def create_smart_expander(title, content_func, expanded=False, help_text=None, category_color=None):
    """Create a smart expander with enhanced styling and progressive disclosure"""
    # Add category-specific styling if provided
    category_class = f"substance-category {category_color}" if category_color else ""
    
    # Enhanced expander with better visual indicators
    expander_icon = "🔽" if expanded else "▶️"
    enhanced_title = f"{expander_icon} {title}"
    
    with st.expander(enhanced_title, expanded=expanded):
        if category_class:
            st.markdown(f'<div class="{category_class}">', unsafe_allow_html=True)
        
        if help_text:
            st.markdown(f"""
            <div class="clinical-guidance">
                <h4>💡 Clinical Guidance</h4>
                <p>{help_text}</p>
                <div class="guidance-actions">
                    <small>💡 <strong>Tip:</strong> Use progressive disclosure - focus on positive findings first</small>
                </div>
            </div>
            """, unsafe_allow_html=True)
        
        # Add collapsible content wrapper for better organization
        st.markdown('<div class="expander-content-wrapper">', unsafe_allow_html=True)
        content_func()
        st.markdown('</div>', unsafe_allow_html=True)
        
        if category_class:
            st.markdown('</div>', unsafe_allow_html=True)

def create_enhanced_tooltip(text, tooltip_content, tooltip_type="info"):
    """Create enhanced tooltips with better accessibility and styling"""
    tooltip_icons = {
        "info": "ℹ️",
        "warning": "⚠️", 
        "clinical": "🩺",
        "dsm5": "📋"
    }
    
    icon = tooltip_icons.get(tooltip_type, "ℹ️")
    
    return f"""
    <div class="tooltip-container enhanced-tooltip">
        {text} {icon}
        <div class="tooltip-text {tooltip_type}-tooltip">
            {tooltip_content}
        </div>
    </div>
    """

def render_section_progress(completed_sections, current_section, total_sections):
    """Render a progress indicator for the current assessment section"""
    progress_html = '<div class="section-progress">'
    
    for i in range(1, total_sections + 1):
        if i < current_section:
            status_class = "completed"
            icon = "✅"
        elif i == current_section:
            status_class = "current"
            icon = "🔄"
        else:
            status_class = "pending"
            icon = "⏳"
        
        progress_html += f'''
        <div class="progress-item {status_class}">
            {icon} Section {i}
        </div>
        '''
    
    progress_html += '</div>'
    st.markdown(progress_html, unsafe_allow_html=True)

def render_dsm5_criteria_guidance(category):
    """Render enhanced clinical guidance for DSM-5 criteria with tooltips and severity indicators"""
    guidance_content = {
        "Alcohol": {
            "text": "DSM-5 requires 2+ criteria within 12 months for diagnosis. Consider tolerance, withdrawal, and social/occupational impairment.",
            "severity_guide": "Mild: 2-3 criteria | Moderate: 4-5 criteria | Severe: 6+ criteria",
            "key_points": ["Tolerance and withdrawal are common", "Binge drinking patterns important", "Legal consequences frequent"]
        },
        "Cannabis": {
            "text": "Cannabis withdrawal was added in DSM-5. Look for irritability, anxiety, and physical discomfort when use stops.",
            "severity_guide": "Mild: 2-3 criteria | Moderate: 4-5 criteria | Severe: 6+ criteria",
            "key_points": ["Withdrawal symptoms now recognized", "High-potency products increase risk", "Synthetic cannabis more dangerous"]
        },
        "Stimulants": {
            "text": "Include both illicit stimulants and prescription ADHD medications when misused. Route of administration affects severity.",
            "severity_guide": "IV/smoking routes indicate higher severity",
            "key_points": ["Psychotic symptoms common", "Cardiovascular risks", "Prescription misuse increasing"]
        },
        "Opioids": {
            "text": "High overdose risk. Document naloxone use and assess for injection drug use complications.",
            "severity_guide": "Any opioid use disorder is serious - focus on harm reduction",
            "key_points": ["Overdose history critical", "Injection use increases risks", "Fentanyl contamination common"]
        },
        "Sedatives_Hypnotics_Anxiolytics": {
            "text": "Dangerous withdrawal syndrome. Assess for both prescribed and illicit use patterns.",
            "severity_guide": "Withdrawal can be life-threatening - medical supervision required",
            "key_points": ["Prescribed vs. illicit use", "Tolerance develops quickly", "Dangerous with alcohol"]
        },
        "Hallucinogens": {
            "text": "Withdrawal criteria not included for most hallucinogens. Focus on tolerance and persistent perceptual disturbances.",
            "severity_guide": "Flashbacks and HPPD are key complications",
            "key_points": ["Flashbacks/HPPD important", "Bad trips cause trauma", "Tolerance varies by substance"]
        },
        "Inhalants": {
            "text": "Particularly dangerous due to sudden death risk. Assess for cognitive and medical complications.",
            "severity_guide": "Any regular use is concerning due to toxicity",
            "key_points": ["Sudden death syndrome", "Cognitive impairment", "Medical complications common"]
        },
        "Tobacco": {
            "text": "Most common substance use disorder. Assess readiness to quit and previous quit attempts.",
            "severity_guide": "Focus on quit attempts and withdrawal severity",
            "key_points": ["E-cigarettes included", "Withdrawal symptoms", "Quit attempt history"]
        },
        "Caffeine": {
            "text": "Caffeine withdrawal is recognized in DSM-5. Assess daily intake and withdrawal symptoms.",
            "severity_guide": "Usually mild but can impact functioning",
            "key_points": ["Daily intake amount", "Withdrawal headaches", "Sleep disruption"]
        },
        "Other_Unknown": {
            "text": "Document unknown substances carefully. Consider synthetic drugs and novel psychoactive substances.",
            "severity_guide": "Unknown substances pose unpredictable risks",
            "key_points": ["Synthetic drugs dangerous", "Unknown composition", "Unpredictable effects"]
        }
    }
    
    if category in guidance_content:
        content = guidance_content[category]
        
        st.markdown(f"""
        <div class="clinical-guidance">
            <h4>🩺 Clinical Guidance - {category.replace('_', '/')}</h4>
            <p><strong>Assessment Focus:</strong> {content['text']}</p>
            <p><strong>Severity Guide:</strong> {content['severity_guide']}</p>
        </div>
        """, unsafe_allow_html=True)
        
        # Add expandable key points
        with st.expander("🔍 Key Clinical Points", expanded=False):
            for point in content['key_points']:
                st.write(f"• {point}")
        
        # Add DSM-5 criteria reference
        if category in DSM5_SUBSTANCE_CATEGORIES:
            criteria = DSM5_SUBSTANCE_CATEGORIES[category].get('dsm5_criteria', [])
            if criteria:
                with st.expander("📋 DSM-5 Criteria Reference", expanded=False):
                    st.markdown("**Check all that apply within the past 12 months:**")
                    for i, criterion in enumerate(criteria, 1):
                        st.write(f"{i}. {criterion}")

def render_medication_class_guidance(med_class):
    """Render enhanced clinical guidance for medication classes with detailed prescribing information"""
    guidance_content = {
        "Antidepressants": {
            "text": "Document trial duration (minimum 4-6 weeks at therapeutic dose), response timeline, and side effect profile. Consider augmentation strategies for partial responders.",
            "key_monitoring": ["Response timeline (2-8 weeks)", "Side effect profile", "Suicidal ideation (especially <25 years)", "Drug interactions"],
            "trial_considerations": ["Adequate dose and duration", "Adherence assessment", "Augmentation if partial response", "Switch vs. augment decision"]
        },
        "Mood_Stabilizers": {
            "text": "Monitor blood levels for lithium and valproate. Assess for metabolic side effects and teratogenic risks in women of childbearing age.",
            "key_monitoring": ["Blood levels (lithium, valproate)", "Kidney function (lithium)", "Liver function (valproate)", "Thyroid function"],
            "trial_considerations": ["Therapeutic blood levels", "Teratogenic risks", "Drug interactions", "Metabolic monitoring"]
        },
        "Antipsychotics": {
            "text": "Monitor for metabolic syndrome, extrapyramidal symptoms, and tardive dyskinesia. Document baseline and ongoing assessments.",
            "key_monitoring": ["Metabolic parameters", "Movement disorders", "Prolactin levels", "QTc interval"],
            "trial_considerations": ["Atypical vs typical choice", "Metabolic risk factors", "Movement disorder history", "Long-acting injections"]
        },
        "Anxiolytics_Benzodiazepines": {
            "text": "High dependence potential. Document indication, duration of use, and withdrawal symptoms. Consider tapering strategies.",
            "key_monitoring": ["Dependence signs", "Tolerance development", "Withdrawal symptoms", "Cognitive effects"],
            "trial_considerations": ["Short-term use preferred", "Tapering schedule", "Alternative treatments", "Abuse potential"]
        },
        "Stimulants": {
            "text": "Monitor cardiovascular effects and growth in children. Assess for diversion and misuse potential.",
            "key_monitoring": ["Blood pressure/heart rate", "Growth parameters (children)", "Sleep and appetite", "Mood changes"],
            "trial_considerations": ["Cardiovascular screening", "Diversion risk", "Drug holidays", "Long-acting formulations"]
        },
        "Sleep_Medications": {
            "text": "Evaluate sleep hygiene and underlying sleep disorders. Consider dependence risk with long-term use.",
            "key_monitoring": ["Sleep quality improvement", "Daytime functioning", "Dependence signs", "Rebound insomnia"],
            "trial_considerations": ["Sleep hygiene first", "Underlying disorders", "Short-term use", "Cognitive behavioral therapy"]
        },
        "Anti_anxiety_Non_Benzo": {
            "text": "Safer alternatives to benzodiazepines. Buspirone requires several weeks for full effect.",
            "key_monitoring": ["Anxiety symptom reduction", "Side effect profile", "Drug interactions", "Response timeline"],
            "trial_considerations": ["Delayed onset (buspirone)", "Lower abuse potential", "Drug interactions", "Adjunctive use"]
        },
        "Other_Psychiatric": {
            "text": "Specialized medications requiring specific monitoring and expertise. Consider consultation when appropriate.",
            "key_monitoring": ["Medication-specific parameters", "Specialized side effects", "Drug interactions", "Efficacy measures"],
            "trial_considerations": ["Specialist consultation", "Specific indications", "Monitoring requirements", "Patient education"]
        }
    }
    
    if med_class in guidance_content:
        content = guidance_content[med_class]
        
        st.markdown(f"""
        <div class="clinical-guidance">
            <h4>💊 Prescribing Guidance - {med_class.replace('_', ' ')}</h4>
            <p><strong>Clinical Focus:</strong> {content['text']}</p>
        </div>
        """, unsafe_allow_html=True)
        
        col1, col2 = st.columns(2)
        
        with col1:
            with st.expander("🔍 Key Monitoring Points", expanded=False):
                for point in content['key_monitoring']:
                    st.write(f"• {point}")
        
        with col2:
            with st.expander("⚖️ Trial Considerations", expanded=False):
                for consideration in content['trial_considerations']:
                    st.write(f"• {consideration}")
        
        # Add medication-specific tooltips
        if med_class in PSYCHIATRIC_MEDICATION_CLASSES:
            with st.expander("💊 Available Medications", expanded=False):
                med_data = PSYCHIATRIC_MEDICATION_CLASSES[med_class]
                for subclass, medications in med_data.items():
                    st.markdown(f"**{subclass.replace('_', ' ')}:**")
                    for med in medications[:5]:  # Show first 5 to avoid clutter
                        st.write(f"• {med}")
                    if len(medications) > 5:
                        st.write(f"• ... and {len(medications) - 5} more")

def render_severity_indicator(severity, criteria_count):
    """Render a visual severity indicator"""
    severity_classes = {
        "No disorder": "severity-none",
        "Mild": "severity-mild", 
        "Moderate": "severity-moderate",
        "Severe": "severity-severe"
    }
    
    css_class = severity_classes.get(severity, "severity-none")
    
    return f"""
    <div class="severity-indicator {css_class}">
        <strong>{severity}</strong> ({criteria_count} criteria)
    </div>
    """

def create_progressive_disclosure_section(title, content_func, expanded=False, help_text=None):
    """Create a progressive disclosure section with optional help text"""
    with st.expander(title, expanded=expanded):
        if help_text:
            st.info(f"💡 {help_text}")
        content_func()

def render_medication_trial_summary(trials):
    """Render a summary of medication trials"""
    if not trials:
        return
    
    st.markdown("#### 📊 Trial Summary")
    
    # Create summary metrics
    total_trials = len(trials)
    responded_trials = len([t for t in trials if t.get('response_rating', 0) >= 3])
    side_effect_trials = len([t for t in trials if t.get('side_effects') and len(t['side_effects']) > 1])
    
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Total Trials", total_trials)
    with col2:
        st.metric("Positive Response", f"{responded_trials}/{total_trials}")
    with col3:
        st.metric("Side Effects", f"{side_effect_trials}/{total_trials}")

def render_substance_use_summary(substance_data):
    """Render an enhanced summary of substance use patterns with progressive disclosure"""
    if not substance_data or 'substances' not in substance_data:
        return
    
    st.markdown("""
    <div class="navigation-aid">
        <span class="icon">📊</span>
        <strong>Substance Use Overview</strong> - Click sections below to expand detailed assessments
    </div>
    """, unsafe_allow_html=True)
    
    substances_used = [cat for cat, data in substance_data['substances'].items() if data.get('used')]
    disorders_present = [cat for cat, data in substance_data['substances'].items() 
                        if data.get('used') and len(data.get('dsm5_criteria', [])) >= 2]
    high_risk = [cat for cat, data in substance_data['substances'].items() 
                if data.get('used') and data.get('severity') == 'Severe']
    current_use = [cat for cat, data in substance_data['substances'].items() 
                  if data.get('used') and data.get('current_use') not in ['Never used', 'Past use only']]
    
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("Substances Used", len(substances_used), 
                 help="Total number of substance categories with documented use")
    with col2:
        st.metric("Current Use", len(current_use),
                 help="Substances currently being used")
    with col3:
        st.metric("Disorders Present", len(disorders_present),
                 help="Categories meeting DSM-5 criteria (2+ symptoms)")
    with col4:
        st.metric("High Risk", len(high_risk),
                 help="Severe substance use disorders requiring immediate attention")
    
    # Progressive disclosure of detailed information
    if substances_used:
        with st.expander("🔍 Detailed Substance Breakdown", expanded=False):
            for substance in substances_used:
                data = substance_data['substances'][substance]
                severity = data.get('severity', 'Not assessed')
                criteria_count = len(data.get('dsm5_criteria', []))
                current_status = data.get('current_use', 'Unknown')
                
                # Color coding based on severity
                if severity == 'Severe':
                    color = "#dc3545"
                    icon = "🔴"
                elif severity == 'Moderate':
                    color = "#fd7e14"
                    icon = "🟠"
                elif severity == 'Mild':
                    color = "#ffc107"
                    icon = "🟡"
                else:
                    color = "#6c757d"
                    icon = "⚪"
                
                st.markdown(f"""
                <div style="border-left: 4px solid {color}; padding: 10px; margin: 8px 0; background: rgba(248,249,250,0.8);">
                    <strong>{icon} {substance.replace('_', '/')}</strong><br>
                    <small>Status: {current_status} | Severity: {severity} | Criteria: {criteria_count}/11</small>
                </div>
                """, unsafe_allow_html=True)
    
    # Smart recommendations based on data
    if high_risk:
        st.error(f"⚠️ **Clinical Alert:** {len(high_risk)} severe substance use disorder(s) identified. Consider immediate intervention.")
    elif disorders_present:
        st.warning(f"📋 **Clinical Note:** {len(disorders_present)} substance use disorder(s) present. Review treatment options.")
    elif substances_used:
        st.info(f"ℹ️ **Assessment Status:** {len(substances_used)} substance(s) documented. Continue detailed assessment as needed.")

def create_quick_access_panel(title, items, help_text=None):
    """Create a quick access panel for navigation or summary information"""
    st.markdown(f"""
    <div class="quick-access-panel">
        <h4>{title}</h4>
    </div>
    """, unsafe_allow_html=True)
    
    if help_text:
        st.info(f"💡 {help_text}")
    
    if items:
        cols = st.columns(min(len(items), 4))  # Max 4 columns for better layout
        for i, item in enumerate(items):
            with cols[i % len(cols)]:
                if isinstance(item, dict):
                    st.metric(item.get('label', ''), item.get('value', ''), 
                             help=item.get('help', ''))
                else:
                    st.write(f"• {item}")

def render_alcohol_assessment(substance_data):
    """Render the alcohol assessment section with enhanced UI"""
    render_dsm5_criteria_guidance("Alcohol")
    
    if 'Alcohol' not in substance_data['substances']:
        substance_data['substances']['Alcohol'] = {'used': False}
    
    alcohol_data = substance_data['substances']['Alcohol']
    
    # Basic alcohol use assessment with enhanced layout
    st.markdown("#### 🔍 Basic Assessment")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        alcohol_data['used'] = st.checkbox(
            "Alcohol Use",
            value=alcohol_data.get('used', False),
            help="Check if patient has ever used alcohol",
            key="alcohol_use_screening"
        )
        
        if alcohol_data['used']:
            alcohol_data['current_use'] = st.selectbox(
                "Current Alcohol Use",
                ["Never used", "Abstinent (>1 year)", "Abstinent (<1 year)", "Occasional social", "Regular use", "Heavy use"],
                index=0 if not alcohol_data.get('current_use') else ["Never used", "Abstinent (>1 year)", "Abstinent (<1 year)", "Occasional social", "Regular use", "Heavy use"].index(alcohol_data.get('current_use')),
                help="Select current pattern of alcohol use"
            )
            
            alcohol_data['age_onset'] = st.number_input(
                "Age First Used Alcohol",
                min_value=5, max_value=80,
                value=alcohol_data.get('age_onset') if alcohol_data.get('age_onset') is not None else 18,
                help="Age when patient first tried alcohol"
            )
    
    with col2:
        if alcohol_data.get('used'):
            alcohol_data['substances'] = st.multiselect(
                "Types of Alcohol Used",
                DSM5_SUBSTANCE_CATEGORIES['Alcohol']['substances'],
                default=alcohol_data.get('substances', []),
                help="Select all types of alcoholic beverages used"
            )
            
            if alcohol_data['current_use'] not in ["Never used", "Abstinent (>1 year)", "Abstinent (<1 year)"]:
                alcohol_data['drinks_per_week'] = st.number_input(
                    "Drinks per Week",
                    min_value=0, max_value=100,
                    value=alcohol_data.get('drinks_per_week', 0),
                    help="Average number of standard drinks per week"
                )
                
                alcohol_data['binge_episodes'] = st.selectbox(
                    "Binge Drinking (5+ drinks)",
                    ["Never", "Monthly", "Weekly", "Multiple times/week", "Daily"],
                    index=0 if not alcohol_data.get('binge_episodes') else ["Never", "Monthly", "Weekly", "Multiple times/week", "Daily"].index(alcohol_data.get('binge_episodes')),
                    help="Frequency of consuming 5+ drinks in a single occasion"
                )
    
    with col3:
        if alcohol_data.get('used'):
            alcohol_data['last_drink'] = st.selectbox(
                "Last Drink",
                ["Never", "Today", "Yesterday", "This week", "This month", "Months ago", "Years ago"],
                index=0 if not alcohol_data.get('last_drink') else ["Never", "Today", "Yesterday", "This week", "This month", "Months ago", "Years ago"].index(alcohol_data.get('last_drink')),
                help="When was the last time alcohol was consumed"
            )
    
    # DSM-5 Criteria Assessment if alcohol is used
    if alcohol_data.get('used'):
        st.markdown("#### 📋 DSM-5 Criteria Assessment")
        
        if 'dsm5_criteria' not in alcohol_data:
            alcohol_data['dsm5_criteria'] = []
        
        criteria_options = DSM5_SUBSTANCE_CATEGORIES['Alcohol']['dsm5_criteria']
        alcohol_data['dsm5_criteria'] = st.multiselect(
            "Select all criteria present in the past 12 months:",
            criteria_options,
            default=alcohol_data.get('dsm5_criteria', []),
            help="Check all DSM-5 criteria that apply to the patient's alcohol use"
        )
        
        # Automatic severity calculation
        criteria_count = len(alcohol_data['dsm5_criteria'])
        if criteria_count >= 6:
            severity = "Severe"
        elif criteria_count >= 4:
            severity = "Moderate"
        elif criteria_count >= 2:
            severity = "Mild"
        else:
            severity = "No disorder"
        
        alcohol_data['severity'] = severity
        
        # Display severity with visual indicator
        st.markdown(render_severity_indicator(severity, criteria_count), unsafe_allow_html=True)
        
        # Clinical consequences if criteria are met
        if criteria_count >= 2:
            st.markdown("#### 🚨 Clinical Impact Assessment")
            
            if 'consequences' not in alcohol_data:
                alcohol_data['consequences'] = []
            
            consequence_options = [
                "Legal problems", "Social/relationship problems", "Occupational/academic problems",
                "Health problems", "Financial problems", "Risky behaviors"
            ]
            
            alcohol_data['consequences'] = st.multiselect(
                "Areas of life impacted by alcohol use:",
                consequence_options,
                default=alcohol_data.get('consequences', []),
                help="Select all areas where alcohol use has caused problems"
            )

def render_smart_navigation_aids(current_data, section_type="substance"):
    """Render smart navigation aids based on current data state"""
    if section_type == "substance" and current_data:
        # Show quick navigation to incomplete sections
        incomplete_categories = []
        for category, data in current_data.get('substances', {}).items():
            if data.get('used') and not data.get('dsm5_criteria'):
                incomplete_categories.append(category.replace('_', '/'))
        
        if incomplete_categories:
            st.markdown("""
            <div class="clinical-guidance">
                <h4>⚠️ Incomplete Assessments</h4>
                <p>The following categories need DSM-5 criteria assessment:</p>
            </div>
            """, unsafe_allow_html=True)
            
            for category in incomplete_categories[:3]:  # Show max 3 for space
                st.warning(f"📋 {category} - DSM-5 criteria needed")
    
    elif section_type == "medication" and current_data:
        # Show medication classes with incomplete trials
        incomplete_classes = []
        for med_class, data in current_data.items():
            if isinstance(data, dict) and data.get('trials'):
                incomplete_trials = [t for t in data['trials'] 
                                   if not t.get('response_rating') or not t.get('duration_weeks')]
                if incomplete_trials:
                    incomplete_classes.append(med_class.replace('_', ' '))
        
        if incomplete_classes:
            st.markdown("""
            <div class="clinical-guidance">
                <h4>📝 Incomplete Medication Trials</h4>
                <p>The following medication classes have incomplete trial information:</p>
            </div>
            """, unsafe_allow_html=True)
            
            for med_class in incomplete_classes[:3]:
                st.info(f"💊 {med_class} - Trial details needed")

# Custom JSON encoder to handle date objects
class DateTimeEncoder(json.JSONEncoder):
    """Custom JSON encoder that handles datetime objects"""
    def default(self, obj):
        if isinstance(obj, (datetime.datetime, datetime.date)):
            return obj.isoformat()
        elif isinstance(obj, datetime.time):
            return obj.isoformat()
        elif hasattr(obj, '__dict__'):
            return obj.__dict__
        return super().default(obj)

def safe_json_dumps(obj, **kwargs):
    """Safely serialize objects to JSON with custom encoder"""
    try:
        return json.dumps(obj, cls=DateTimeEncoder, **kwargs)
    except (TypeError, ValueError) as e:
        # Fallback: convert problematic objects to strings
        try:
            def convert_to_serializable(item):
                if isinstance(item, (datetime.datetime, datetime.date, datetime.time)):
                    return item.isoformat()
                elif isinstance(item, dict):
                    return {k: convert_to_serializable(v) for k, v in item.items()}
                elif isinstance(item, list):
                    return [convert_to_serializable(i) for i in item]
                elif hasattr(item, '__dict__'):
                    return str(item)
                else:
                    return item
            
            converted_obj = convert_to_serializable(obj)
            return json.dumps(converted_obj, **kwargs)
        except Exception as fallback_error:
            st.error(f"JSON serialization failed: {fallback_error}")
            return json.dumps({"error": "Serialization failed", "original_error": str(e)})

# Enhanced Database Manager
class DatabaseManager:
    """Enhanced database manager with connection pooling and retry logic"""
    
    def __init__(self, db_path: str = 'psychiatric_assessments.db', 
                 max_connections: int = 5, timeout: float = 30.0):
        self.db_path = db_path
        self.max_connections = max_connections
        self.timeout = timeout
        self.connection_pool = []
        self.pool_lock = threading.Lock()
        self.logger = logging.getLogger(__name__)
        
        # Initialize database
        self._initialize_database()
    
    def _initialize_database(self):
        """Initialize database with proper settings"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=self.timeout)
            conn.execute("PRAGMA foreign_keys = ON")
            conn.execute("PRAGMA journal_mode = WAL")
            conn.execute("PRAGMA synchronous = NORMAL")
            conn.execute("PRAGMA cache_size = -64000")  # 64MB cache
            conn.execute("PRAGMA busy_timeout = 30000")  # 30 second busy timeout
            
            # Create tables using existing function
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Database initialization failed: {e}")
            raise
    
    @contextmanager
    def get_connection(self):
        """Get database connection with automatic cleanup"""
        conn = None
        try:
            # Try to get connection from pool
            with self.pool_lock:
                if self.connection_pool:
                    conn = self.connection_pool.pop()
                else:
                    conn = sqlite3.connect(
                        self.db_path, 
                        timeout=self.timeout,
                        check_same_thread=False
                    )
                    conn.execute("PRAGMA foreign_keys = ON")
                    conn.execute("PRAGMA busy_timeout = 30000")
            
            yield conn
            
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            self.logger.error(f"Database error: {e}")
            raise
        finally:
            # Return connection to pool or close
            if conn:
                try:
                    with self.pool_lock:
                        if len(self.connection_pool) < self.max_connections:
                            self.connection_pool.append(conn)
                        else:
                            conn.close()
                except Exception as e:
                    self.logger.error(f"Error returning connection to pool: {e}")
                    try:
                        conn.close()
                    except:
                        pass
    
    def execute_with_retry(self, operation_func, max_retries: int = 3):
        """Execute database operation with retry logic"""
        last_exception = None
        
        for attempt in range(max_retries):
            try:
                with self.get_connection() as conn:
                    return operation_func(conn)
            
            except (sqlite3.OperationalError, sqlite3.DatabaseError) as e:
                last_exception = e
                if attempt < max_retries - 1:
                    # Exponential backoff
                    wait_time = 0.1 * (2 ** attempt)
                    time.sleep(wait_time)
                    self.logger.warning(f"Database operation failed, retrying in {wait_time}s: {e}")
                else:
                    self.logger.error(f"Database operation failed after {max_retries} attempts: {e}")
            
            except Exception as e:
                self.logger.error(f"Unexpected database error: {e}")
                raise
        
        raise last_exception

# Global database manager instance
db_manager = DatabaseManager()

# Session State Validator
class SessionStateValidator:
    """Comprehensive session state validation and management"""
    
    # Define the expected structure
    EXPECTED_STRUCTURE = {
        'patient_data': dict,
        'current_section': int,
        'patient_id': (str, type(None)),
        'assessment_start_time': type(None),  # Will be datetime
        'last_auto_save': (int, float),
        'auto_save_enabled': bool,
        'section_states': dict,
        'validation_errors': dict,
        'substance_details': dict,
        'assessment_history': list,
        'current_assessment_index': int,
        'db_initialized': bool,
        'current_view': str,
        'editing_assessment_id': (int, type(None)),
        'patient_code_entered': bool,
        'show_reset_confirm': bool,
        'auto_save_indicator': bool
    }
    
    @classmethod
    def validate_and_fix_session_state(cls) -> List[str]:
        """Validate session state and fix common issues"""
        issues_fixed = []
        
        # Check and fix basic structure
        for key, expected_type in cls.EXPECTED_STRUCTURE.items():
            if key not in st.session_state:
                default_value = cls._get_default_value(key, expected_type)
                st.session_state[key] = default_value
                issues_fixed.append(f"Added missing key: {key}")
            
            elif not cls._is_valid_type(st.session_state[key], expected_type):
                default_value = cls._get_default_value(key, expected_type)
                st.session_state[key] = default_value
                issues_fixed.append(f"Fixed invalid type for: {key}")
        
        # Validate patient data structure
        if not isinstance(st.session_state.patient_data, dict):
            st.session_state.patient_data = {}
            issues_fixed.append("Fixed patient_data structure")
        
        # Validate current section bounds
        max_sections = 15  # Based on your app structure
        if not (0 <= st.session_state.current_section <= max_sections):
            st.session_state.current_section = 0
            issues_fixed.append("Reset current_section to valid range")
        
        # Validate view state
        valid_views = ['assessment', 'dashboard']
        if st.session_state.current_view not in valid_views:
            st.session_state.current_view = 'assessment'
            issues_fixed.append("Reset current_view to valid value")
        
        return issues_fixed
    
    @classmethod
    def _get_default_value(cls, key: str, expected_type):
        """Get default value for a session state key"""
        defaults = {
            'patient_data': {},
            'current_section': 0,
            'patient_id': None,
            'assessment_start_time': datetime.datetime.now(),
            'last_auto_save': time.time(),
            'auto_save_enabled': True,
            'section_states': {},
            'validation_errors': {},
            'substance_details': {},
            'assessment_history': [],
            'current_assessment_index': 0,
            'db_initialized': False,
            'current_view': 'assessment',
            'editing_assessment_id': None,
            'patient_code_entered': False,
            'show_reset_confirm': False,
            'auto_save_indicator': False
        }
        return defaults.get(key, None)
    
    @classmethod
    def _is_valid_type(cls, value, expected_type):
        """Check if value matches expected type"""
        if isinstance(expected_type, tuple):
            return isinstance(value, expected_type)
        return isinstance(value, expected_type)

def validate_session_state_integrity() -> List[str]:
    """Validate session state for data integrity and fix common issues"""
    errors = []
    
    try:
        # Check if assessment_data exists and is properly structured
        if 'assessment_data' not in st.session_state:
            st.session_state.assessment_data = {}
            errors.append("Initialized missing assessment_data")
        
        assessment_data = st.session_state.assessment_data
        if not isinstance(assessment_data, dict):
            st.session_state.assessment_data = {}
            errors.append("Fixed corrupted assessment_data (was not a dictionary)")
            return errors
        
        # Validate substance use section structure
        if 'substance_use' in assessment_data:
            substance_data = assessment_data['substance_use']
            if not isinstance(substance_data, dict):
                assessment_data['substance_use'] = {}
                errors.append("Fixed corrupted substance_use data")
            else:
                # Ensure all DSM-5 categories have proper structure
                for category in DSM5_SUBSTANCE_CATEGORIES.keys():
                    if category in substance_data and not isinstance(substance_data[category], dict):
                        substance_data[category] = {}
                        errors.append(f"Fixed corrupted {category} data structure")
        
        # Validate medication history section structure
        if 'past_psychiatric_history' in assessment_data:
            pph = assessment_data['past_psychiatric_history']
            if isinstance(pph, dict) and 'medication_history' in pph:
                med_data = pph['medication_history']
                if not isinstance(med_data, dict):
                    pph['medication_history'] = {}
                    errors.append("Fixed corrupted medication_history data")
                else:
                    # Ensure all medication classes have proper trial structure
                    for med_class, data in med_data.items():
                        if isinstance(data, dict) and 'trials' in data:
                            if not isinstance(data['trials'], list):
                                data['trials'] = []
                                errors.append(f"Fixed corrupted {med_class} trials data")
        
        # Validate current section tracking
        if 'current_section' not in st.session_state:
            st.session_state.current_section = 0
            errors.append("Initialized missing current_section")
        elif not isinstance(st.session_state.current_section, int) or st.session_state.current_section < 0:
            st.session_state.current_section = 0
            errors.append("Fixed invalid current_section value")
        
    except Exception as e:
        errors.append(f"Session state validation error: {str(e)}")
        # Reset to safe state
        st.session_state.assessment_data = {}
        st.session_state.current_section = 0
    
    return errors

def initialize_and_validate_session_state():
    """Initialize and validate session state at app startup"""
    validator = SessionStateValidator()
    issues_fixed = validator.validate_and_fix_session_state()
    
    # Also run enhanced session state validation
    additional_issues = validate_session_state_integrity()
    
    all_issues = issues_fixed + additional_issues
    
    if all_issues:
        st.info(f"🔧 Fixed {len(all_issues)} session state issues")
        with st.expander("View Details"):
            for issue in all_issues:
                st.write(f"• {issue}")

# Configure page
st.set_page_config(
    page_title="Psychiatric Assessment System",
    page_icon="🧠",
    layout="wide",
    initial_sidebar_state="expanded",
    menu_items={
        'Get Help': None,
        'Report a bug': None,
        'About': "Secure Psychiatric Assessment System v2.0"
    }
)

# Custom CSS for professional medical interface
st.markdown("""
<style>
    .main-header {
        font-size: 2.8rem;
        font-weight: 800;
        color: #1e3a8a;
        text-align: center;
        margin-bottom: 2rem;
        background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 50%, #7c3aed 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .section-header {
        font-size: 1.6rem;
        font-weight: 700;
        color: #1e40af;
        border-left: 5px solid #3b82f6;
        padding-left: 1rem;
        margin: 1.5rem 0;
        background: linear-gradient(90deg, rgba(59,130,246,0.1) 0%, transparent 100%);
        padding: 1rem;
        border-radius: 0 10px 10px 0;
    }
    .subsection-header {
        font-size: 1.2rem;
        font-weight: 600;
        color: #1e40af;
        margin: 1rem 0 0.5rem 0;
        border-bottom: 2px solid #e5e7eb;
        padding-bottom: 0.3rem;
    }
    .metric-card {
        background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
        padding: 1.5rem;
        border-radius: 15px;
        color: white;
        text-align: center;
        margin: 0.5rem 0;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .risk-high { background: linear-gradient(135deg, #dc2626, #b91c1c); }
    .risk-moderate { background: linear-gradient(135deg, #ea580c, #c2410c); }
    .risk-low { background: linear-gradient(135deg, #16a34a, #15803d); }
    
    .stSelectbox > div > div {
        background-color: #f8fafc;
        border: 2px solid #e2e8f0;
        border-radius: 8px;
    }
    .stButton > button {
        background: linear-gradient(135deg, #3b82f6, #8b5cf6);
        color: white;
        border: none;
        border-radius: 25px;
        padding: 0.7rem 2.5rem;
        font-weight: 700;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }
    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    }
    .clinical-note {
        background: #fef3c7;
        border-left: 4px solid #f59e0b;
        padding: 1rem;
        margin: 1rem 0;
        border-radius: 0 8px 8px 0;
    }
    .progress-container {
        background: #f1f5f9;
        padding: 1rem;
        border-radius: 10px;
        margin: 1rem 0;
    }
    .validation-error {
        color: #dc2626;
        font-weight: 600;
        margin-top: 0.5rem;
    }
    .auto-save-indicator {
        position: fixed;
        bottom: 20px;
        right: 20px;
        background: #10b981;
        color: white;
        padding: 10px 20px;
        border-radius: 25px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        z-index: 1000;
        transition: opacity 0.3s ease;
    }
    .template-button {
        background: #e5e7eb;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        padding: 0.3rem 0.8rem;
        margin: 0.2rem;
        font-size: 0.85rem;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    .template-button:hover {
        background: #d1d5db;
    }
    .dashboard-card {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        margin-bottom: 1rem;
        border-left: 4px solid #3b82f6;
    }
    .patient-card {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        margin-bottom: 1rem;
        border-left: 4px solid #8b5cf6;
        transition: all 0.3s ease;
    }
    .patient-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
    }

    /* Fix for multiselect dropdown behavior - keep dropdowns open longer */
    .stMultiSelect > div > div > div {
        background-color: #f8fafc;
        border: 2px solid #e2e8f0;
        border-radius: 8px;
        position: relative;
        z-index: 9999;
    }

    /* Ensure multiselect dropdown stays open for multiple selections */
    .stMultiSelect [data-baseweb="select"] {
        background-color: #f8fafc;
        position: relative;
        z-index: 9999;
    }

    /* Improve multiselect option visibility and prevent closing */
    .stMultiSelect [data-baseweb="popover"] {
        z-index: 99999 !important;
        position: fixed !important;
    }

    /* Style multiselect options */
    .stMultiSelect [role="option"] {
        padding: 8px 12px;
        border-bottom: 1px solid #e2e8f0;
        cursor: pointer;
    }

    .stMultiSelect [role="option"]:hover {
        background-color: #e0f2fe !important;
    }

    /* Selected option styling */
    .stMultiSelect [aria-selected="true"] {
        background-color: #3b82f6 !important;
        color: white !important;
    }

    /* Multiselect tag styling */
    .stMultiSelect [data-baseweb="tag"] {
        background-color: #3b82f6;
        color: white;
        border-radius: 15px;
        margin: 2px;
        padding: 4px 8px;
    }

    /* Keep multiselect menu open during interactions */
    .stMultiSelect [data-baseweb="select"] [data-baseweb="input"] {
        cursor: pointer;
    }

    /* Enhanced navigation button styling */
    .stButton[data-testid="baseButton-secondary"] > button {
        width: 100%;
        text-align: left;
        padding: 8px 12px;
        margin: 2px 0;
        border-radius: 8px;
        border: 1px solid #e2e8f0;
        background: #f8fafc;
        color: #374151;
        font-size: 0.9rem;
        transition: all 0.2s ease;
    }

    .stButton[data-testid="baseButton-secondary"] > button:hover {
        background: #e0f2fe;
        border-color: #3b82f6;
        transform: translateX(2px);
    }

    /* Current section highlighting */
    .stButton[data-testid="baseButton-secondary"] > button[aria-pressed="true"] {
        background: #3b82f6;
        color: white;
        border-color: #3b82f6;
    }
    
    .search-box {
        background: #f8fafc;
        border: 2px solid #e2e8f0;
        border-radius: 8px;
        padding: 0.7rem;
        margin-bottom: 1rem;
    }
    .filter-tag {
        display: inline-block;
        background: #e0e7ff;
        color: #4338ca;
        padding: 0.3rem 0.8rem;
        border-radius: 20px;
        margin: 0.2rem;
        font-size: 0.85rem;
    }
    
    /* Improved form styling */
    .form-section {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        margin-bottom: 1.5rem;
    }
    
    /* Assessment progress bar */
    .progress-bar-container {
        width: 100%;
        background-color: #e2e8f0;
        border-radius: 10px;
        margin: 1rem 0;
    }
    
    .progress-bar {
        height: 20px;
        border-radius: 10px;
        background: linear-gradient(90deg, #3b82f6, #8b5cf6);
        transition: width 0.5s ease;
    }
    
    /* Section completion indicator */
    .section-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }
    
    .section-complete {
        background-color: #10b981;
    }
    
    .section-current {
        background-color: #3b82f6;
    }
    
    .section-incomplete {
        background-color: #e2e8f0;
    }
    
    /* Responsive adjustments */
    @media (max-width: 768px) {
        .main-header {
            font-size: 2rem;
        }
        
        .section-header {
            font-size: 1.3rem;
        }
        
        .subsection-header {
            font-size: 1.1rem;
        }
    }
</style>
""", unsafe_allow_html=True)

# Database Functions
def create_connection():
    """Create a database connection to the SQLite database with improved error handling"""
    try:
        conn = sqlite3.connect(
            'psychiatric_assessments.db',
            timeout=30.0,  # 30 second timeout to prevent locking
            check_same_thread=False
        )
        # Enable foreign key constraints and WAL mode for better concurrency
        conn.execute("PRAGMA foreign_keys = ON")
        conn.execute("PRAGMA journal_mode = WAL")

        # Fix datetime adapter deprecation warning
        sqlite3.register_adapter(datetime.datetime, lambda val: val.isoformat())
        sqlite3.register_converter("timestamp", lambda val: datetime.datetime.fromisoformat(val.decode()))
        return conn
    except Error as e:
        st.error(f"Database error: {e}")
        return None

def create_tables():
    """Create tables if they don't exist with enhanced constraints"""
    conn = create_connection()
    if conn is not None:
        try:
            cursor = conn.cursor()

            # Enhanced patients table with constraints
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS patients (
                patient_id TEXT PRIMARY KEY,
                age INTEGER CHECK (age >= 0 AND age <= 150),
                gender TEXT,
                sex_assigned TEXT,
                marital_status TEXT,
                children TEXT,
                education TEXT,
                occupation TEXT,
                employment_status TEXT,
                ethnicity TEXT,
                living_situation TEXT,
                housing_stability TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            
            # Enhanced assessments table with constraints
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS assessments (
                assessment_id INTEGER PRIMARY KEY AUTOINCREMENT,
                patient_id TEXT NOT NULL,
                assessment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                chief_complaint TEXT,
                history_present_illness TEXT,
                past_psychiatric_history TEXT,
                past_medical_history TEXT,
                family_history TEXT,
                social_developmental_history TEXT,
                substance_use TEXT,
                mental_state_examination TEXT,
                cognitive_assessment TEXT,
                risk_assessment TEXT,
                laboratory_investigations TEXT,
                clinical_scales TEXT,
                diagnostic_formulation TEXT,
                treatment_planning TEXT,
                follow_up_monitoring TEXT,
                suicide_risk_level TEXT CHECK (suicide_risk_level IN ('Low', 'Moderate', 'High', 'Imminent')),
                primary_diagnosis TEXT,
                phq9_score INTEGER CHECK (phq9_score >= 0 AND phq9_score <= 27),
                gad7_score INTEGER CHECK (gad7_score >= 0 AND gad7_score <= 21),
                duration_minutes REAL CHECK (duration_minutes >= 0),
                completion_percentage REAL CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
                data_json TEXT,
                created_by TEXT DEFAULT 'system',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (patient_id) REFERENCES patients (patient_id) ON DELETE CASCADE
            )
            ''')
            
            # Create indexes for faster queries
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_patient_id ON assessments (patient_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_assessment_date ON assessments (assessment_date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_primary_diagnosis ON assessments (primary_diagnosis)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_suicide_risk ON assessments (suicide_risk_level)')
            
            conn.commit()
        except Error as e:
            st.error(f"Error creating tables: {e}")
        finally:
            conn.close()

def save_patient_data(patient_data):
    """Save patient demographics to the database"""
    try:
        # Extract and validate demographics data
        demographics = patient_data.get('demographics', {})

        # Validate patient ID
        if not st.session_state.patient_id:
            st.error("Invalid patient ID format")
            return False

        # Validate and sanitize all input fields
        validated_data = {}
        for key, value in demographics.items():
            if isinstance(value, str):
                validated_data[key] = value.strip()
            else:
                validated_data[key] = value

        # Validate age if present
        if 'age' in validated_data and validated_data['age'] is not None:
            if not (0 <= validated_data['age'] <= 150):
                st.error("Invalid age value")
                return False

        # Use database operations
        conn = create_connection()
        if conn is None:
            return False
            
        cursor = conn.cursor()
        cursor.execute('''
            INSERT OR REPLACE INTO patients (
                patient_id, age, gender, sex_assigned, marital_status, children,
                education, occupation, employment_status, ethnicity, living_situation,
                housing_stability
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            st.session_state.patient_id,
            validated_data.get('age'),
            validated_data.get('gender'),
            validated_data.get('sex_assigned'),
            validated_data.get('marital_status'),
            validated_data.get('children'),
            validated_data.get('education'),
            validated_data.get('occupation'),
            validated_data.get('employment_status'),
            ', '.join(validated_data.get('ethnicity', [])),
            validated_data.get('living_situation'),
            validated_data.get('housing_stability')
        ))
        
        conn.commit()
        conn.close()
        return True

    except Exception as e:
        st.error(f"Error saving patient data: {e}")
        return False

def save_assessment_data(assessment_data):
    """Save assessment data to the database"""
    conn = create_connection()
    if conn is not None:
        try:
            cursor = conn.cursor()
            
            # Extract key ML-relevant fields
            diagnostic_data = assessment_data.get('diagnostic_formulation', {})
            risk_data = assessment_data.get('risk_assessment', {})
            scales_data = assessment_data.get('clinical_scales', {})
            
            # Calculate duration and completion
            duration = (datetime.datetime.now() - st.session_state.assessment_start_time).total_seconds() / 60
            
            # Create mapping between section names and data keys
            section_key_mapping = {
                "Demographics & Identifying Information": "demographics",
                "Chief Complaint & Referral": "chief_complaint",
                "History of Present Illness": "history_present_illness",
                "Past Psychiatric History": "past_psychiatric_history",
                "Past Medical History": "past_medical_history",
                "Family History": "family_history",
                "Social & Developmental History": "social_developmental_history",
                "Substance Use Assessment": "substance_use",
                "Mental State Examination": "mental_state_examination",
                "Cognitive Assessment": "cognitive_assessment",
                "Risk Assessment": "risk_assessment",
                "Laboratory & Investigations": "laboratory_investigations",
                "Clinical Scales & Ratings": "clinical_scales",
                "Diagnostic Formulation": "diagnostic_formulation",
                "Treatment Planning": "treatment_planning",
                "Follow-up & Monitoring": "follow_up_monitoring"
            }
            
            sections = list(section_key_mapping.keys())
            completed = 0
            for section in sections:
                key = section_key_mapping[section]
                if key in assessment_data and assessment_data.get(key):
                    completed += 1
            completion = completed / len(sections) * 100 if sections else 0

            # Ensure patient exists first
            try:
                cursor.execute("SELECT patient_id FROM patients WHERE patient_id = ?", (st.session_state.patient_id,))
                patient_check = cursor.fetchone()

                if not patient_check:
                    # Try to save patient data first if demographics exist
                    if 'demographics' in assessment_data and assessment_data['demographics']:
                        st.warning("Patient record not found. Attempting to save patient demographics first...")
                        if not save_patient_data(assessment_data):
                            st.error("Failed to save patient demographics. Cannot save assessment.")
                            return False
                        st.info("Patient demographics saved successfully. Proceeding with assessment save...")
                    else:
                        st.error("Patient record not found and no demographics data available. Please complete patient demographics first.")
                        return False
            except Exception as e:
                st.error(f"Error checking patient record: {e}")
                return False

            # Perform comprehensive validation before saving
            validation_results = perform_comprehensive_validation(assessment_data)
            
            # Display validation warnings (don't prevent saving, allow partial data)
            all_errors = []
            for error_type, errors in validation_results.items():
                if errors:
                    all_errors.extend([f"{error_type.replace('_', ' ').title()}: {error}" for error in errors])
            
            if all_errors:
                st.warning("⚠️ Data validation warnings detected:")
                for error in all_errors[:10]:  # Limit to first 10 to avoid UI clutter
                    st.warning(f"• {error}")
                if len(all_errors) > 10:
                    st.warning(f"... and {len(all_errors) - 10} more warnings")

            # Insert assessment data
            cursor.execute('''
            INSERT INTO assessments (
                patient_id, chief_complaint, history_present_illness, past_psychiatric_history,
                past_medical_history, family_history, social_developmental_history, substance_use,
                mental_state_examination, cognitive_assessment, risk_assessment,
                laboratory_investigations, clinical_scales, diagnostic_formulation,
                treatment_planning, follow_up_monitoring, suicide_risk_level,
                primary_diagnosis, phq9_score, gad7_score, duration_minutes,
                completion_percentage, data_json
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                st.session_state.patient_id,
                safe_json_dumps(assessment_data.get('chief_complaint', {})),
                safe_json_dumps(assessment_data.get('history_present_illness', {})),
                safe_json_dumps(assessment_data.get('past_psychiatric_history', {})),
                safe_json_dumps(assessment_data.get('past_medical_history', {})),
                safe_json_dumps(assessment_data.get('family_history', {})),
                safe_json_dumps(assessment_data.get('social_developmental_history', {})),
                safe_json_dumps(assessment_data.get('substance_use', {})),
                safe_json_dumps(assessment_data.get('mental_state_examination', {})),
                safe_json_dumps(assessment_data.get('cognitive_assessment', {})),
                safe_json_dumps(assessment_data.get('risk_assessment', {})),
                safe_json_dumps(assessment_data.get('laboratory_investigations', {})),
                safe_json_dumps(assessment_data.get('clinical_scales', {})),
                safe_json_dumps(assessment_data.get('diagnostic_formulation', {})),
                safe_json_dumps(assessment_data.get('treatment_planning', {})),
                safe_json_dumps(assessment_data.get('follow_up_monitoring', {})),
                risk_data.get('suicide', {}).get('risk_level'),
                diagnostic_data.get('diagnoses', {}).get('primary'),
                scales_data.get('depression', {}).get('phq9_total'),
                scales_data.get('anxiety', {}).get('gad7_total'),
                duration,
                completion,
                safe_json_dumps(assessment_data)
            ))
            
            conn.commit()
            conn.close()
            return True
        except Error as e:
            st.error(f"Error saving assessment data: {e}")
            conn.close()
            return False
    return False

def get_all_patients():
    """Get all patients from the database"""
    conn = create_connection()
    if conn is not None:
        try:
            patients = pd.read_sql_query("SELECT * FROM patients ORDER BY created_at DESC", conn)
            conn.close()
            return patients
        except Error as e:
            st.error(f"Error retrieving patients: {e}")
            conn.close()
            return pd.DataFrame()
    return pd.DataFrame()

def get_all_assessments():
    """Get all assessments from the database with patient information"""
    conn = create_connection()
    if conn is not None:
        try:
            query = """
            SELECT a.*, p.age, p.gender, p.marital_status, p.education, p.occupation
            FROM assessments a
            LEFT JOIN patients p ON a.patient_id = p.patient_id
            ORDER BY a.assessment_date DESC
            """
            assessments = pd.read_sql_query(query, conn)
            conn.close()
            return assessments
        except Error as e:
            st.error(f"Error retrieving assessments: {e}")
            conn.close()
            return pd.DataFrame()
    return pd.DataFrame()

def get_patient_assessments(patient_id):
    """Get all assessments for a specific patient"""
    conn = create_connection()
    if conn is not None:
        try:
            assessments = pd.read_sql_query(
                "SELECT * FROM assessments WHERE patient_id = ? ORDER BY assessment_date DESC", 
                conn, 
                params=(patient_id,)
            )
            conn.close()
            return assessments
        except Error as e:
            st.error(f"Error retrieving assessments: {e}")
            conn.close()
            return pd.DataFrame()
    return pd.DataFrame()

def get_assessment_by_id(assessment_id):
    """Get a specific assessment by ID with improved error handling"""
    conn = create_connection()
    if conn is not None:
        try:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM assessments WHERE assessment_id = ?", (assessment_id,))
            assessment = cursor.fetchone()

            if assessment:
                columns = [desc[0] for desc in cursor.description]
                assessment_dict = dict(zip(columns, assessment))

                # Fix JSON parsing issue - ensure data_json is a string
                if 'data_json' in assessment_dict and assessment_dict['data_json'] is not None:
                    if not isinstance(assessment_dict['data_json'], str):
                        assessment_dict['data_json'] = str(assessment_dict['data_json'])

                conn.close()
                return assessment_dict
            conn.close()
            return None
        except Error as e:
            st.error(f"Error retrieving assessment: {e}")
            conn.close()
            return None
    return None

def update_assessment(assessment_id, assessment_data):
    """Update an existing assessment"""
    conn = create_connection()
    if conn is not None:
        try:
            cursor = conn.cursor()
            
            # Extract key ML-relevant fields
            diagnostic_data = assessment_data.get('diagnostic_formulation', {})
            risk_data = assessment_data.get('risk_assessment', {})
            scales_data = assessment_data.get('clinical_scales', {})
            
            # Calculate duration and completion
            duration = (datetime.datetime.now() - st.session_state.assessment_start_time).total_seconds() / 60
            
            # Create mapping between section names and data keys
            section_key_mapping = {
                "Demographics & Identifying Information": "demographics",
                "Chief Complaint & Referral": "chief_complaint",
                "History of Present Illness": "history_present_illness",
                "Past Psychiatric History": "past_psychiatric_history",
                "Past Medical History": "past_medical_history",
                "Family History": "family_history",
                "Social & Developmental History": "social_developmental_history",
                "Substance Use Assessment": "substance_use",
                "Mental State Examination": "mental_state_examination",
                "Cognitive Assessment": "cognitive_assessment",
                "Risk Assessment": "risk_assessment",
                "Laboratory & Investigations": "laboratory_investigations",
                "Clinical Scales & Ratings": "clinical_scales",
                "Diagnostic Formulation": "diagnostic_formulation",
                "Treatment Planning": "treatment_planning",
                "Follow-up & Monitoring": "follow_up_monitoring"
            }
            
            sections = list(section_key_mapping.keys())
            completed = 0
            for section in sections:
                key = section_key_mapping[section]
                if key in assessment_data and assessment_data.get(key):
                    completed += 1
            completion = completed / len(sections) * 100 if sections else 0
            
            # Perform comprehensive validation before updating
            validation_results = perform_comprehensive_validation(assessment_data)
            
            # Display validation warnings (don't prevent updating, allow partial data)
            all_errors = []
            for error_type, errors in validation_results.items():
                if errors:
                    all_errors.extend([f"{error_type.replace('_', ' ').title()}: {error}" for error in errors])
            
            if all_errors:
                st.warning("⚠️ Data validation warnings detected:")
                for error in all_errors[:10]:  # Limit to first 10 to avoid UI clutter
                    st.warning(f"• {error}")
                if len(all_errors) > 10:
                    st.warning(f"... and {len(all_errors) - 10} more warnings")
            
            # Update assessment data
            cursor.execute('''
            UPDATE assessments SET
                chief_complaint = ?,
                history_present_illness = ?,
                past_psychiatric_history = ?,
                past_medical_history = ?,
                family_history = ?,
                social_developmental_history = ?,
                substance_use = ?,
                mental_state_examination = ?,
                cognitive_assessment = ?,
                risk_assessment = ?,
                laboratory_investigations = ?,
                clinical_scales = ?,
                diagnostic_formulation = ?,
                treatment_planning = ?,
                follow_up_monitoring = ?,
                suicide_risk_level = ?,
                primary_diagnosis = ?,
                phq9_score = ?,
                gad7_score = ?,
                duration_minutes = ?,
                completion_percentage = ?,
                data_json = ?
            WHERE assessment_id = ?
            ''', (
                safe_json_dumps(assessment_data.get('chief_complaint', {})),
                safe_json_dumps(assessment_data.get('history_present_illness', {})),
                safe_json_dumps(assessment_data.get('past_psychiatric_history', {})),
                safe_json_dumps(assessment_data.get('past_medical_history', {})),
                safe_json_dumps(assessment_data.get('family_history', {})),
                safe_json_dumps(assessment_data.get('social_developmental_history', {})),
                safe_json_dumps(assessment_data.get('substance_use', {})),
                safe_json_dumps(assessment_data.get('mental_state_examination', {})),
                safe_json_dumps(assessment_data.get('cognitive_assessment', {})),
                safe_json_dumps(assessment_data.get('risk_assessment', {})),
                safe_json_dumps(assessment_data.get('laboratory_investigations', {})),
                safe_json_dumps(assessment_data.get('clinical_scales', {})),
                safe_json_dumps(assessment_data.get('diagnostic_formulation', {})),
                safe_json_dumps(assessment_data.get('treatment_planning', {})),
                safe_json_dumps(assessment_data.get('follow_up_monitoring', {})),
                risk_data.get('suicide', {}).get('risk_level'),
                diagnostic_data.get('diagnoses', {}).get('primary'),
                scales_data.get('depression', {}).get('phq9_total'),
                scales_data.get('anxiety', {}).get('gad7_total'),
                duration,
                completion,
                safe_json_dumps(assessment_data),
                assessment_id
            ))
            
            conn.commit()
            conn.close()
            return True
        except Error as e:
            st.error(f"Error updating assessment: {e}")
            conn.close()
            return False
    return False

def delete_assessment(assessment_id):
    """Delete an assessment from the database"""
    conn = create_connection()
    if conn is not None:
        try:
            cursor = conn.cursor()
            cursor.execute("DELETE FROM assessments WHERE assessment_id = ?", (assessment_id,))
            conn.commit()
            conn.close()
            return True
        except Error as e:
            st.error(f"Error deleting assessment: {e}")
            conn.close()
            return False
    return False

def get_ml_dataset():
    """Get a dataset optimized for ML training"""
    conn = create_connection()
    if conn is not None:
        try:
            # Query to get ML-relevant data
            query = '''
            SELECT 
                p.patient_id, p.age, p.gender, p.sex_assigned, p.marital_status, 
                p.children, p.education, p.occupation, p.employment_status, 
                p.ethnicity, p.living_situation, p.housing_stability,
                a.assessment_date, a.suicide_risk_level, a.primary_diagnosis, 
                a.phq9_score, a.gad7_score, a.duration_minutes, a.completion_percentage,
                a.data_json
            FROM assessments a
            JOIN patients p ON a.patient_id = p.patient_id
            ORDER BY a.assessment_date DESC
            '''
            
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            # Process JSON data to extract additional features
            if not df.empty and 'data_json' in df.columns:
                # Initialize empty DataFrames for each category
                substance_df = pd.DataFrame()
                risk_df = pd.DataFrame()
                diagnostic_df = pd.DataFrame()
                
                for idx, row in df.iterrows():
                    try:
                        data = json.loads(row['data_json'])
                        
                        # Extract substance use data
                        substance_data = data.get('substance_use', {})
                        if substance_data:
                            substance_row = pd.json_normalize(substance_data)
                            substance_row.index = [idx]
                            substance_df = pd.concat([substance_df, substance_row])
                        
                        # Extract risk assessment data
                        risk_data = data.get('risk_assessment', {})
                        if risk_data:
                            risk_row = pd.json_normalize(risk_data)
                            risk_row.index = [idx]
                            risk_df = pd.concat([risk_df, risk_row])
                        
                        # Extract diagnostic data
                        diagnostic_data = data.get('diagnostic_formulation', {})
                        if diagnostic_data:
                            diagnostic_row = pd.json_normalize(diagnostic_data)
                            diagnostic_row.index = [idx]
                            diagnostic_df = pd.concat([diagnostic_df, diagnostic_row])
                    except Exception as e:
                        st.warning(f"Error processing JSON for patient {row['patient_id']}: {e}")
                        continue
                
                # Prefix columns to avoid conflicts
                if not substance_df.empty:
                    substance_df.columns = ['substance_' + col for col in substance_df.columns]
                
                if not risk_df.empty:
                    risk_df.columns = ['risk_' + col for col in risk_df.columns]
                
                if not diagnostic_df.empty:
                    diagnostic_df.columns = ['diagnostic_' + col for col in diagnostic_df.columns]
                
                # Combine all data
                df = df.drop('data_json', axis=1)
                df = pd.concat([df, substance_df, risk_df, diagnostic_df], axis=1)
            
            return df
        except Error as e:
            st.error(f"Error retrieving ML dataset: {e}")
            conn.close()
            return pd.DataFrame()
    return pd.DataFrame()

def validate_database():
    """Validate database integrity and test functions"""
    conn = create_connection()
    if conn is not None:
        try:
            cursor = conn.cursor()
            
            # Test 1: Check if tables exist
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [table[0] for table in cursor.fetchall()]
            
            if 'patients' not in tables or 'assessments' not in tables:
                conn.close()
                return False, "Required tables not found"
            
            # Test 2: Check if required columns exist
            cursor.execute("PRAGMA table_info(patients)")
            patient_columns = [column[1] for column in cursor.fetchall()]
            
            required_patient_columns = ['patient_id', 'age', 'gender', 'created_at']
            for col in required_patient_columns:
                if col not in patient_columns:
                    conn.close()
                    return False, f"Missing column in patients table: {col}"
            
            cursor.execute("PRAGMA table_info(assessments)")
            assessment_columns = [column[1] for column in cursor.fetchall()]
            
            required_assessment_columns = ['assessment_id', 'patient_id', 'assessment_date', 'data_json']
            for col in required_assessment_columns:
                if col not in assessment_columns:
                    conn.close()
                    return False, f"Missing column in assessments table: {col}"
            
            # Test 3: Insert and retrieve test data
            test_patient_id = "TEST-1234"
            
            # Insert test patient
            cursor.execute('''
            INSERT OR REPLACE INTO patients (
                patient_id, age, gender, sex_assigned, marital_status, children,
                education, occupation, employment_status, ethnicity, living_situation,
                housing_stability
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                test_patient_id, 35, "Male", "Male", "Married", "2",
                "Bachelor's degree", "Engineer", "Employed full-time", "White/Caucasian", 
                "With spouse/partner", "Stable housing"
            ))
            
            # Insert test assessment
            cursor.execute('''
            INSERT INTO assessments (
                patient_id, chief_complaint, history_present_illness, suicide_risk_level,
                primary_diagnosis, phq9_score, gad7_score, duration_minutes,
                completion_percentage, data_json
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                test_patient_id, 
                safe_json_dumps({"complaint": "Depression"}),
                safe_json_dumps({"history": "Chronic depression"}),
                "Moderate",
                "Major Depressive Disorder",
                15,
                12,
                45.2,
                85.0,
                safe_json_dumps({"test": "data"})
            ))
            
            # Retrieve test data
            cursor.execute("SELECT * FROM patients WHERE patient_id = ?", (test_patient_id,))
            test_patient = cursor.fetchone()
            
            cursor.execute("SELECT * FROM assessments WHERE patient_id = ?", (test_patient_id,))
            test_assessment = cursor.fetchone()
            
            # Clean up test data
            cursor.execute("DELETE FROM patients WHERE patient_id = ?", (test_patient_id,))
            cursor.execute("DELETE FROM assessments WHERE patient_id = ?", (test_patient_id,))
            
            conn.commit()
            conn.close()
            
            if test_patient is None or test_assessment is None:
                return False, "Failed to insert or retrieve test data"
            
            return True, "Database validation successful"
        except Error as e:
            conn.close()
            return False, f"Database validation error: {e}"
    return False, "Failed to connect to database"

# Initialize comprehensive session state with validation
def initialize_session_state():
    """Initialize all necessary session state variables with validation"""
    initialize_and_validate_session_state()
    
    # Set any additional defaults that aren't covered by validator
    if 'templates' not in st.session_state:
        st.session_state.templates = {
            'depression': "Patient reports persistent low mood, anhedonia, and fatigue. Symptoms interfere with daily functioning.",
            'anxiety': "Patient reports excessive worry, restlessness, and physical symptoms of anxiety including palpitations and shortness of breath.",
            'psychosis': "Patient reports auditory hallucinations and paranoid ideation. Thought process is disorganized with loose associations.",
            'substance_use': "Patient acknowledges substance use with negative consequences including relationship and occupational problems.",
            'trauma': "Patient reports history of trauma with current symptoms including hypervigilance, nightmares, and avoidance behaviors."
        }

initialize_session_state()

# Initialize database if not already done
if not st.session_state.db_initialized:
    create_tables()
    st.session_state.db_initialized = True

# Clinical scales and questionnaires
def phq9_questions():
    return [
        "Little interest or pleasure in doing things",
        "Feeling down, depressed, or hopeless", 
        "Trouble falling or staying asleep, or sleeping too much",
        "Feeling tired or having little energy",
        "Poor appetite or overeating",
        "Feeling bad about yourself or that you are a failure",
        "Trouble concentrating on things",
        "Moving or speaking slowly or being fidgety/restless",
        "Thoughts that you would be better off dead or hurting yourself"
    ]

def gad7_questions():
    return [
        "Feeling nervous, anxious, or on edge",
        "Not being able to stop or control worrying",
        "Worrying too much about different things", 
        "Trouble relaxing",
        "Being so restless that it is hard to sit still",
        "Becoming easily annoyed or irritable",
        "Feeling afraid, as if something awful might happen"
    ]

def mse_options():
    return {
        'appearance': ['Well-groomed', 'Unkempt', 'Bizarre dress', 'Age-appropriate', 'Malnourished', 'Obese'],
        'behavior': ['Cooperative', 'Agitated', 'Withdrawn', 'Hostile', 'Psychomotor retardation', 'Psychomotor agitation', 'Catatonic'],
        'speech': ['Normal rate/rhythm', 'Pressured', 'Slow', 'Loud', 'Soft', 'Monotone', 'Dysarthric', 'Stuttering'],
        'mood': ['Euthymic', 'Depressed', 'Elevated', 'Irritable', 'Anxious', 'Angry', 'Euphoric', 'Dysthymic'],
        'affect': ['Appropriate', 'Inappropriate', 'Constricted', 'Blunted', 'Flat', 'Labile', 'Expansive'],
        'thought_process': ['Linear', 'Tangential', 'Circumstantial', 'Flight of ideas', 'Loose associations', 'Thought blocking', 'Perseveration'],
        'thought_content': ['No abnormalities', 'Delusions', 'Obsessions', 'Compulsions', 'Phobias', 'Suicidal ideation', 'Homicidal ideation'],
        'perceptions': ['No abnormalities', 'Auditory hallucinations', 'Visual hallucinations', 'Tactile hallucinations', 'Olfactory hallucinations', 'Illusions'],
        'cognition': ['Intact', 'Impaired attention', 'Memory impairment', 'Disoriented', 'Abstract thinking impaired', 'Poor judgment'],
        'insight': ['Good', 'Fair', 'Poor', 'Absent', 'Partial'],
        'judgment': ['Good', 'Fair', 'Poor', 'Impaired']
    }

# Helper functions for validation and data management
def validate_date(date_input, allow_future=False, field_name="Date"):
    """Validate date input and return error message if invalid"""
    if date_input is None:
        return f"{field_name} is required"
    
    today = datetime.date.today()
    if not allow_future and date_input > today:
        return f"{field_name} cannot be in the future"
    
    return None

def validate_number(value, min_val, max_val, field_name="Value"):
    """Validate numeric input and return error message if invalid"""
    if value is None:
        return f"{field_name} is required"
    
    if not min_val <= value <= max_val:
        return f"{field_name} must be between {min_val} and {max_val}"
    
    return None

def validate_required(value, field_name="Field"):
    """Validate required field and return error message if empty"""
    if value is None or value == "":
        return f"{field_name} is required"
    return None

def validate_cross_section_consistency(assessment_data: Dict[str, Any]) -> List[str]:
    """Validate consistency between substance use and medication history sections"""
    errors = []
    
    if not isinstance(assessment_data, dict):
        return ["Assessment data must be a dictionary"]
    
    substance_data = assessment_data.get('substance_use', {})
    medication_data = assessment_data.get('past_psychiatric_history', {}).get('medication_history', {})
    
    # Check for substance use that might interact with psychiatric medications
    if isinstance(substance_data, dict) and isinstance(medication_data, dict):
        # Check for alcohol use with sedating medications
        alcohol_use = substance_data.get('Alcohol', {}).get('used', False)
        sedative_use = substance_data.get('Sedatives_Hypnotics_Anxiolytics', {}).get('used', False)
        
        # Check if patient is on benzodiazepines and using alcohol or other sedatives
        benzo_trials = medication_data.get('anxiolytics_benzodiazepines', {}).get('trials', [])
        active_benzo = any(trial.get('currently_taking', False) for trial in benzo_trials if isinstance(trial, dict))
        
        if active_benzo and (alcohol_use or sedative_use):
            errors.append("Warning: Patient reports current benzodiazepine use with alcohol or sedative use - potential dangerous interaction")
        
        # Check for stimulant medication misuse
        stimulant_trials = medication_data.get('stimulants', {}).get('trials', [])
        active_stimulant = any(trial.get('currently_taking', False) for trial in stimulant_trials if isinstance(trial, dict))
        stimulant_misuse = substance_data.get('Stimulants', {}).get('used', False)
        
        if active_stimulant and stimulant_misuse:
            errors.append("Note: Patient has both prescribed stimulants and reports stimulant substance use - assess for medication misuse")
    
    return errors

def validate_dsm5_criteria_consistency(substance_data: Dict[str, Any]) -> List[str]:
    """Validate DSM-5 criteria selections for logical consistency"""
    errors = []
    
    if not isinstance(substance_data, dict):
        return ["Substance data must be a dictionary"]
    
    for category, data in substance_data.items():
        if category in DSM5_SUBSTANCE_CATEGORIES and isinstance(data, dict):
            criteria_met = data.get('dsm5_criteria', [])
            if isinstance(criteria_met, list):
                # Check for logical inconsistencies
                if 'Withdrawal' in criteria_met and not data.get('withdrawal', False):
                    errors.append(f"{category}: DSM-5 withdrawal criterion selected but withdrawal symptoms not reported")
                
                if 'Tolerance' in criteria_met and not data.get('tolerance', False):
                    errors.append(f"{category}: DSM-5 tolerance criterion selected but tolerance not reported")
                
                # Check severity calculation
                severity = get_dsm5_severity(len(criteria_met))
                if len(criteria_met) >= 2 and not data.get('used', False):
                    errors.append(f"{category}: DSM-5 criteria met ({severity}) but substance use not indicated")
    
    return errors

def validate_medication_trial_consistency(medication_data: Dict[str, Any]) -> List[str]:
    """Validate medication trial data for logical consistency"""
    errors = []
    
    if not isinstance(medication_data, dict):
        return ["Medication data must be a dictionary"]
    
    for med_class, data in medication_data.items():
        if isinstance(data, dict) and 'trials' in data:
            trials = data['trials']
            if isinstance(trials, list):
                for i, trial in enumerate(trials):
                    if isinstance(trial, dict):
                        # Check for logical inconsistencies
                        response = trial.get('response_rating')
                        adherence = trial.get('adherence_rating')
                        discontinuation = trial.get('discontinuation_reason', '')
                        
                        # High response but discontinued for ineffectiveness
                        if response and response >= 4 and 'ineffective' in discontinuation.lower():
                            errors.append(f"{med_class} trial {i+1}: High response rating ({response}) inconsistent with discontinuation for ineffectiveness")
                        
                        # Low adherence but no mention in discontinuation reason
                        if adherence and adherence <= 2 and discontinuation and 'adherence' not in discontinuation.lower() and 'compliance' not in discontinuation.lower():
                            errors.append(f"{med_class} trial {i+1}: Low adherence rating ({adherence}) but discontinuation reason doesn't mention adherence issues")
                        
                        # Currently taking but has discontinuation reason
                        if trial.get('currently_taking', False) and discontinuation:
                            errors.append(f"{med_class} trial {i+1}: Marked as currently taking but has discontinuation reason")
    
    return errors



def perform_comprehensive_validation(assessment_data: Dict[str, Any]) -> Dict[str, List[str]]:
    """Perform comprehensive validation across all sections"""
    validation_results = {
        'substance_use_errors': [],
        'medication_errors': [],
        'cross_section_errors': [],
        'dsm5_consistency_errors': [],
        'medication_consistency_errors': [],
        'session_state_errors': []
    }
    
    try:
        # Individual section validations
        if 'substance_use' in assessment_data:
            validation_results['substance_use_errors'] = validate_substance_use_data(assessment_data['substance_use'])
            validation_results['dsm5_consistency_errors'] = validate_dsm5_criteria_consistency(assessment_data['substance_use'])
        
        if 'past_psychiatric_history' in assessment_data and 'medication_history' in assessment_data['past_psychiatric_history']:
            med_data = assessment_data['past_psychiatric_history']['medication_history']
            validation_results['medication_errors'] = validate_medication_history_data(med_data)
            validation_results['medication_consistency_errors'] = validate_medication_trial_consistency(med_data)
        
        # Cross-section validation
        validation_results['cross_section_errors'] = validate_cross_section_consistency(assessment_data)
        
        # Session state validation
        validation_results['session_state_errors'] = validate_session_state_integrity()
        
    except Exception as e:
        validation_results['session_state_errors'].append(f"Comprehensive validation error: {str(e)}")
    
    return validation_results

def recover_corrupted_data(assessment_data: Dict[str, Any]) -> Dict[str, Any]:
    """Attempt to recover corrupted assessment data"""
    recovered_data = {}
    recovery_log = []
    
    try:
        # Ensure basic structure exists
        if not isinstance(assessment_data, dict):
            recovered_data = {}
            recovery_log.append("Recovered from non-dictionary assessment data")
        else:
            recovered_data = assessment_data.copy()
        
        # Fix substance use section
        if 'substance_use' in recovered_data:
            substance_data = recovered_data['substance_use']
            if not isinstance(substance_data, dict):
                recovered_data['substance_use'] = {}
                recovery_log.append("Recovered corrupted substance_use section")
            else:
                # Fix individual substance categories
                for category in DSM5_SUBSTANCE_CATEGORIES.keys():
                    if category in substance_data and not isinstance(substance_data[category], dict):
                        substance_data[category] = {'used': False}
                        recovery_log.append(f"Recovered corrupted {category} data")
                    
                    # Ensure DSM-5 criteria is a list
                    if category in substance_data and 'dsm5_criteria' in substance_data[category]:
                        if not isinstance(substance_data[category]['dsm5_criteria'], list):
                            substance_data[category]['dsm5_criteria'] = []
                            recovery_log.append(f"Fixed {category} DSM-5 criteria format")
        
        # Fix medication history section
        if 'past_psychiatric_history' in recovered_data:
            pph = recovered_data['past_psychiatric_history']
            if isinstance(pph, dict) and 'medication_history' in pph:
                med_data = pph['medication_history']
                if not isinstance(med_data, dict):
                    pph['medication_history'] = {}
                    recovery_log.append("Recovered corrupted medication_history section")
                else:
                    # Fix medication trials
                    for med_class, data in med_data.items():
                        if isinstance(data, dict) and 'trials' in data:
                            if not isinstance(data['trials'], list):
                                data['trials'] = []
                                recovery_log.append(f"Fixed {med_class} trials format")
                            else:
                                # Fix individual trials
                                for i, trial in enumerate(data['trials']):
                                    if not isinstance(trial, dict):
                                        data['trials'][i] = {}
                                        recovery_log.append(f"Fixed {med_class} trial {i+1} format")
        
        # Log recovery actions
        if recovery_log:
            st.warning(f"🔧 Data recovery performed: {len(recovery_log)} issues fixed")
            with st.expander("Recovery Details"):
                for log_entry in recovery_log:
                    st.write(f"• {log_entry}")
        
    except Exception as e:
        st.error(f"Data recovery failed: {str(e)}")
        # Return minimal safe structure
        recovered_data = {
            'substance_use': {},
            'past_psychiatric_history': {'medication_history': {}}
        }
    
    return recovered_data

def auto_fix_validation_errors(assessment_data: Dict[str, Any]) -> Dict[str, Any]:
    """Automatically fix common validation errors where possible"""
    fixed_data = assessment_data.copy() if isinstance(assessment_data, dict) else {}
    fixes_applied = []
    
    try:
        # Fix substance use data
        if 'substance_use' in fixed_data and isinstance(fixed_data['substance_use'], dict):
            substance_data = fixed_data['substance_use']
            
            for category, data in substance_data.items():
                if category in DSM5_SUBSTANCE_CATEGORIES and isinstance(data, dict):
                    # Auto-fix: If withdrawal is true but used is false, set used to true
                    if data.get('withdrawal', False) and not data.get('used', False):
                        data['used'] = True
                        fixes_applied.append(f"Auto-fixed {category}: Set 'used' to true because withdrawal was reported")
                    
                    # Auto-fix: If tolerance is true but used is false, set used to true
                    if data.get('tolerance', False) and not data.get('used', False):
                        data['used'] = True
                        fixes_applied.append(f"Auto-fixed {category}: Set 'used' to true because tolerance was reported")
                    
                    # Auto-fix: If DSM-5 criteria are met but used is false, set used to true
                    criteria_count = len(data.get('dsm5_criteria', []))
                    if criteria_count >= 2 and not data.get('used', False):
                        data['used'] = True
                        fixes_applied.append(f"Auto-fixed {category}: Set 'used' to true because DSM-5 criteria are met")
        
        # Fix medication history data
        if 'past_psychiatric_history' in fixed_data:
            pph = fixed_data['past_psychiatric_history']
            if isinstance(pph, dict) and 'medication_history' in pph:
                med_data = pph['medication_history']
                if isinstance(med_data, dict):
                    for med_class, data in med_data.items():
                        if isinstance(data, dict) and 'trials' in data:
                            trials = data['trials']
                            if isinstance(trials, list):
                                for i, trial in enumerate(trials):
                                    if isinstance(trial, dict):
                                        # Auto-fix: If currently taking but has discontinuation reason, clear discontinuation
                                        if trial.get('currently_taking', False) and trial.get('discontinuation_reason'):
                                            trial['discontinuation_reason'] = ''
                                            fixes_applied.append(f"Auto-fixed {med_class} trial {i+1}: Cleared discontinuation reason for currently taking medication")
        
        if fixes_applied:
            st.info(f"🔧 Auto-fixed {len(fixes_applied)} validation issues:")
            for fix in fixes_applied:
                st.info(f"• {fix}")
    
    except Exception as e:
        st.error(f"Auto-fix failed: {str(e)}")
    
    return fixed_data

def calculate_suicide_risk_level(risk_data):
    """Calculate suicide risk level based on assessment data with improved logic"""
    try:
        if not isinstance(risk_data, dict):
            return 'Low'  # Default to low risk if invalid data

        suicide_data = risk_data.get('suicide', {})
        if not isinstance(suicide_data, dict):
            return 'Low'

        # Initialize risk score for more precise calculation
        risk_score = 0

        # Current suicidal ideation (highest weight)
        current_si = suicide_data.get('current_si', 'None')
        si_scores = {
            'None': 0,
            'Passive (wish to be dead)': 1,
            'Active ideation without plan': 2,
            'Active ideation with plan': 3,
            'Active ideation with intent': 4
        }
        risk_score += si_scores.get(current_si, 0) * 3

        # Previous attempts
        previous_attempts = suicide_data.get('previous_attempts', 'None')
        if previous_attempts and previous_attempts != 'None':
            attempt_scores = {
                'None': 0,
                '1 attempt': 1,
                '2-3 attempts': 2,
                '4-5 attempts': 3,
                'More than 5 attempts': 4
            }
            risk_score += attempt_scores.get(previous_attempts, 0) * 2

        # Means access
        means_access = suicide_data.get('means_access', 'No access')
        if means_access == 'Immediate access':
            risk_score += 2
        elif means_access == 'Some access':
            risk_score += 1

        # Family history
        family_suicide = suicide_data.get('family_suicide', 'No')
        if family_suicide == 'Yes - completed':
            risk_score += 2
        elif family_suicide == 'Yes - attempted':
            risk_score += 1

        # Additional risk factors
        if suicide_data.get('substance_use_current', False):
            risk_score += 1
        if suicide_data.get('social_isolation', False):
            risk_score += 1
        if suicide_data.get('recent_loss', False):
            risk_score += 1

        # Convert score to risk level
        if risk_score >= 12:
            return 'Imminent'
        elif risk_score >= 8:
            return 'High'
        elif risk_score >= 4:
            return 'Moderate'
        else:
            return 'Low'

    except Exception as e:
        # Log error and return moderate risk as safe default
        st.warning(f"Error calculating suicide risk: {e}")
        return 'Moderate'

def auto_save_progress():
    """Auto-save progress every 30 seconds"""
    if st.session_state.auto_save_enabled:
        current_time = time.time()
        if current_time - st.session_state.last_auto_save > 30:
            try:
                # Save patient demographics
                save_patient_data(st.session_state.patient_data)
                
                # Save assessment data
                if st.session_state.editing_assessment_id:
                    # Update existing assessment
                    update_assessment(st.session_state.editing_assessment_id, st.session_state.patient_data)
                else:
                    # Create new assessment
                    save_assessment_data(st.session_state.patient_data)
                
                st.session_state.last_auto_save = current_time
                st.session_state.auto_save_indicator = True
                return True
            except Exception as e:
                st.error(f"Auto-save failed: {e}")
                return False
    return False

def get_template_text(template_type):
    """Get template text for common clinical findings"""
    return st.session_state.templates.get(template_type, "")

def form_based_multiselect(label, options, default_values=None, form_key=None, help_text=None):
    """Generic form-based multiselect to prevent dropdown closing"""
    if form_key is None:
        form_key = label.lower().replace(' ', '_').replace('/', '_').replace('-', '_') + '_form'
    
    with st.form(form_key):
        selections = st.multiselect(
            label,
            options,
            default=default_values or [],
            help=help_text or "Select all that apply - form prevents dropdown from closing"
        )
        
        submitted = st.form_submit_button(f"💾 Update {label}", use_container_width=True)
        
        if submitted:
            st.success(f"✅ {label} updated successfully!")
            if st.session_state.get('auto_save_enabled', True):
                try:
                    save_assessment_data(st.session_state.patient_data)
                    st.info("💾 Auto-saved to database")
                except Exception as e:
                    st.warning(f"Auto-save failed: {str(e)}")
            return selections
        
        return default_values or []

def create_form_based_multiselect_section():
    """Use st.form to prevent dropdown closing after each selection"""
    st.markdown("### 🎯 Primary Symptom Categories")
    
    # Get current data or initialize
    if 'history_present_illness' not in st.session_state.patient_data:
        st.session_state.patient_data['history_present_illness'] = {}
    
    hpi = st.session_state.patient_data['history_present_illness']
    
    # Mood Disorders Form
    with st.expander("😔 Mood Disorders", expanded=False):
        with st.form("mood_disorders_form"):
            st.markdown("**Major Depressive Disorder Symptoms:**")
            
            # Initialize nested dict if not exists
            if 'mood_symptoms' not in hpi:
                hpi['mood_symptoms'] = {}
            
            mdd_symptoms = st.multiselect(
                "MDD Symptoms (DSM-5-TR)",
                ["Depressed mood", "Anhedonia", "Weight loss/gain", "Insomnia/hypersomnia", 
                 "Psychomotor agitation/retardation", "Fatigue", "Worthlessness/guilt", 
                 "Concentration problems", "Suicidal ideation"],
                default=hpi['mood_symptoms'].get('mdd_symptoms', []),
                help="Select all symptoms that apply - dropdown will stay open for multiple selections"
            )
            
            st.markdown("**Bipolar Disorder Symptoms:**")
            col1, col2 = st.columns(2)
            
            with col1:
                manic_symptoms = st.multiselect(
                    "Manic/Hypomanic Episodes",
                    ["Elevated mood", "Grandiosity", "Decreased sleep need", "Talkativeness", 
                     "Flight of ideas", "Distractibility", "Increased activity", "Poor judgment"],
                    default=hpi['mood_symptoms'].get('manic_symptoms', [])
                )
            
            with col2:
                mixed_features = st.multiselect(
                    "Mixed Features",
                    ["Depressed mood during mania", "Manic symptoms during depression", 
                     "Rapid cycling", "Irritability"],
                    default=hpi['mood_symptoms'].get('mixed_features', [])
                )
            
            # Form submission
            submitted = st.form_submit_button("💾 Update Mood Symptoms", use_container_width=True)
            
            if submitted:
                # Update session state only when form is submitted
                hpi['mood_symptoms'].update({
                    'mdd_symptoms': mdd_symptoms,
                    'manic_symptoms': manic_symptoms,
                    'mixed_features': mixed_features
                })
                st.success("✅ Mood symptoms updated successfully!")
                
                # Optional: Auto-save to database
                if st.session_state.get('auto_save_enabled', True):
                    try:
                        save_assessment_data(st.session_state.patient_data)
                        st.info("💾 Auto-saved to database")
                    except Exception as e:
                        st.warning(f"Auto-save failed: {str(e)}")

    # Anxiety Disorders Form
    with st.expander("😰 Anxiety Disorders", expanded=False):
        with st.form("anxiety_disorders_form"):
            if 'anxiety_symptoms' not in hpi:
                hpi['anxiety_symptoms'] = {}
            
            col1, col2 = st.columns(2)
            with col1:
                gad_symptoms = st.multiselect(
                    "Generalized Anxiety Disorder",
                    ["Excessive worry", "Restlessness", "Fatigue", "Concentration problems", 
                     "Irritability", "Muscle tension", "Sleep disturbance"],
                    default=hpi['anxiety_symptoms'].get('gad_symptoms', [])
                )
                
                panic_symptoms = st.multiselect(
                    "Panic Disorder",
                    ["Panic attacks", "Palpitations", "Sweating", "Trembling", "Shortness of breath", 
                     "Choking sensation", "Chest pain", "Nausea", "Dizziness", "Fear of dying", 
                     "Fear of losing control", "Derealization"],
                    default=hpi['anxiety_symptoms'].get('panic_symptoms', [])
                )
            
            with col2:
                social_anxiety = st.multiselect(
                    "Social Anxiety Disorder",
                    ["Fear of social situations", "Fear of judgment", "Avoidance of social events", 
                     "Physical symptoms in social situations", "Performance anxiety"],
                    default=hpi['anxiety_symptoms'].get('social_anxiety', [])
                )
                
                specific_phobias = st.multiselect(
                    "Specific Phobias",
                    ["Animal phobia", "Natural environment", "Blood/injection/injury", 
                     "Situational phobia", "Other specific fears"],
                    default=hpi['anxiety_symptoms'].get('specific_phobias', [])
                )
            
            # Form submission
            anxiety_submitted = st.form_submit_button("💾 Update Anxiety Symptoms", use_container_width=True)
            
            if anxiety_submitted:
                hpi['anxiety_symptoms'].update({
                    'gad_symptoms': gad_symptoms,
                    'panic_symptoms': panic_symptoms,
                    'social_anxiety': social_anxiety,
                    'specific_phobias': specific_phobias
                })
                st.success("✅ Anxiety symptoms updated successfully!")
                
                if st.session_state.get('auto_save_enabled', True):
                    try:
                        save_assessment_data(st.session_state.patient_data)
                        st.info("💾 Auto-saved to database")
                    except Exception as e:
                        st.warning(f"Auto-save failed: {str(e)}")

def insert_template(template_type, text_area_key):
    """Insert template text into a text area"""
    template = get_template_text(template_type)
    if template and text_area_key in st.session_state:
        current_text = st.session_state[text_area_key]
        if current_text:
            new_text = current_text + "\n\n" + template
        else:
            new_text = template
        st.session_state[text_area_key] = new_text
        st.rerun()

def save_current_assessment():
    """Save current assessment to database with improved error handling"""
    if not st.session_state.patient_data:
        st.error("No assessment data to save.")
        return False

    if not st.session_state.patient_id:
        st.error("No patient ID found. Please start a new assessment.")
        return False

    try:
        # Save patient demographics first
        patient_saved = save_patient_data(st.session_state.patient_data)
        if not patient_saved:
            st.error("Failed to save patient demographics.")
            return False

        # Save assessment data
        if st.session_state.editing_assessment_id:
            # Update existing assessment
            assessment_saved = update_assessment(st.session_state.editing_assessment_id, st.session_state.patient_data)
            if assessment_saved:
                st.success(f"Assessment updated successfully!")
                # Reset editing state
                st.session_state.editing_assessment_id = None
                return True
            else:
                st.error("Failed to update assessment.")
                return False
        else:
            # Create new assessment
            assessment_saved = save_assessment_data(st.session_state.patient_data)
            if assessment_saved:
                st.success(f"Assessment saved successfully!")
                return True
            else:
                st.error("Failed to save assessment.")
                return False

    except Exception as e:
        st.error(f"Error saving assessment: {e}")
        return False

def load_assessment(assessment_id):
    """Load assessment from database"""
    assessment_data = get_assessment_by_id(assessment_id)
    if assessment_data:
        # Extract patient ID
        patient_id = assessment_data['patient_id']
        st.session_state.patient_id = patient_id
        
        # Load assessment data
        try:
            data_json = json.loads(assessment_data['data_json'])
            st.session_state.patient_data = data_json
            st.session_state.editing_assessment_id = assessment_id
            
            # Set assessment start time
            st.session_state.assessment_start_time = datetime.datetime.now()
            
            st.success(f"Loaded assessment for {patient_id}")
            st.session_state.current_view = 'assessment'
            st.rerun()
        except json.JSONDecodeError:
            st.error("Failed to parse assessment data")
            return False
    return False

def new_assessment():
    """Start a new assessment"""
    st.session_state.patient_data = {}
    st.session_state.patient_id = None  # Will be set by user input
    st.session_state.current_section = 0
    st.session_state.editing_assessment_id = None
    st.session_state.current_view = 'assessment'
    st.session_state.patient_code_entered = False
    st.success("New assessment started!")
    st.rerun()

# Main application entry point
def main():
    """Main application"""
    # Show appropriate view based on current state
    if st.session_state.current_view == 'dashboard':
        show_dashboard_view()
    else:
        show_assessment_view()

# Navigation
def show_assessment_view():
    """Display the assessment form"""

    # Sidebar navigation with enhanced progress tracking - always show
    with st.sidebar:
        st.markdown("### 🧠 Psychiatric Assessment Navigation")

        # Show patient code or prompt to enter one
        if st.session_state.patient_id:
            st.markdown(f"**Patient Code:** `{st.session_state.patient_id}`")
        else:
            st.markdown("**Patient Code:** *Not set*")

        st.markdown("---")
        
        sections = [
            "Demographics & Identifying Information",
            "Chief Complaint & Referral", 
            "History of Present Illness",
            "Past Psychiatric History",
            "Past Medical History", 
            "Family History",
            "Social & Developmental History",
            "Substance Use Assessment",
            "Mental State Examination",
            "Cognitive Assessment", 
            "Risk Assessment",
            "Laboratory & Investigations",
            "Clinical Scales & Ratings",
            "Diagnostic Formulation",
            "Treatment Planning",
            "Follow-up & Monitoring"
        ]
        
        # Enhanced section navigation with visual indicators
        st.markdown("### 📋 Section Navigation")

        # Create section completion mapping
        section_data_keys = {
            0: "demographics",
            1: "chief_complaint",
            2: "history_present_illness",
            3: "past_psychiatric_history",
            4: "past_medical_history",
            5: "family_history",
            6: "social_developmental_history",
            7: "substance_use",
            8: "mental_state_examination",
            9: "cognitive_assessment",
            10: "risk_assessment",
            11: "laboratory_investigations",
            12: "clinical_scales",
            13: "diagnostic_formulation",
            14: "treatment_planning",
            15: "follow_up_monitoring"
        }

        # Display sections with completion status
        for i, section in enumerate(sections):
            # Check if section is completed
            data_key = section_data_keys.get(i)
            is_completed = data_key in st.session_state.patient_data and st.session_state.patient_data.get(data_key)
            is_current = i == st.session_state.current_section

            # Create status indicator
            if is_completed:
                status_icon = "✅"
            elif is_current and st.session_state.patient_id:
                status_icon = "📝"
            else:
                status_icon = "⭕"

            # Create clickable section button with better formatting
            section_label = f"{status_icon} {i+1:2d}. {section}"

            # Simple navigation - just need patient ID
            button_disabled = not st.session_state.patient_id
            button_help = f"Go to section {i+1}: {section}" if not button_disabled else "Enter patient code first"

            if st.button(section_label, key=f"nav_section_{i}",
                        help=button_help,
                        disabled=button_disabled,
                        use_container_width=True):
                if st.session_state.patient_id:  # Double-check patient ID exists
                    st.session_state.current_section = i
                    st.rerun()

        # Enhanced progress tracking
        st.markdown("---")
        st.markdown("### 📊 Progress Overview")

        progress = (st.session_state.current_section + 1) / len(sections)
        st.markdown('<div class="progress-container">', unsafe_allow_html=True)
        st.progress(progress)
        st.write(f"**Current Progress:** {progress:.0%} ({st.session_state.current_section + 1}/{len(sections)})")

        # Calculate completion percentage based on data
        completed_sections = len([k for k in st.session_state.patient_data.keys() if st.session_state.patient_data.get(k)])
        completion_rate = completed_sections / len(sections)
        st.write(f"**Data Completion:** {completion_rate:.0%} ({completed_sections}/{len(sections)} sections)")
        st.markdown('</div>', unsafe_allow_html=True)

        # Assessment metadata
        st.markdown("---")
        st.markdown("### ⏱️ Session Info")
        st.markdown(f"**Started:** {st.session_state.assessment_start_time.strftime('%Y-%m-%d %H:%M')}")
        duration = datetime.datetime.now() - st.session_state.assessment_start_time
        st.markdown(f"**Duration:** {str(duration).split('.')[0]}")
        
        # Assessment management
        st.markdown("---")
        st.markdown("### 📁 Assessment Management")
        
        if st.button("🆕 New Assessment"):
            new_assessment()
        
        if st.button("📊 View Dashboard"):
            st.session_state.current_view = 'dashboard'
            st.rerun()
        
        # Auto-save toggle
        auto_save_enabled = st.checkbox("Enable Auto-save", value=st.session_state.auto_save_enabled)
        st.session_state.auto_save_enabled = auto_save_enabled

    # Handle patient code input if not set
    if not st.session_state.patient_id:
        st.markdown('<h1 class="main-header">🧠 New Psychiatric Assessment</h1>', unsafe_allow_html=True)
        st.markdown('<div class="section-header">Patient Code Entry</div>', unsafe_allow_html=True)
        st.markdown("Please enter a patient code to identify this case (no real names or personal identifiers):")

        col1, col2 = st.columns([3, 1])
        with col1:
            patient_code = st.text_input("Patient Code", placeholder="e.g., CASE-001, PT-ABC123", key="patient_code_input")
        with col2:
            if st.button("Set Code", type="primary"):
                if patient_code.strip():
                    st.session_state.patient_id = patient_code.strip().upper()
                    st.session_state.patient_code_entered = True
                    st.success(f"Patient code set: {st.session_state.patient_id}")
                    st.rerun()
                else:
                    st.error("Please enter a valid patient code")

        st.info("💡 Use a unique code that helps you identify this case later (e.g., CASE-001, PT-ABC123)")
        return  # Exit function until patient code is set

    # Main header
    st.markdown('<h1 class="main-header">🧠 Comprehensive Psychiatric Assessment System</h1>', unsafe_allow_html=True)

    # Auto-save indicator
    if st.session_state.auto_save_indicator:
        st.markdown('<div class="auto-save-indicator">✅ Auto-saved</div>', unsafe_allow_html=True)
        st.session_state.auto_save_indicator = False

    # Section 0: Demographics & Identifying Information (Simplified for ML)
    if st.session_state.current_section == 0:
        st.markdown('<div class="section-header">Demographics & Identifying Information</div>', unsafe_allow_html=True)
        st.info(f"Patient Code: **{st.session_state.patient_id}**")
        
        # Validation errors display
        if 'demographics_errors' in st.session_state.validation_errors:
            for error in st.session_state.validation_errors['demographics_errors']:
                st.error(error)
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.markdown('<div class="subsection-header">Basic Demographics</div>', unsafe_allow_html=True)
            # Changed default to None to avoid bias in ML
            age = st.number_input("Age", min_value=1, max_value=120, value=None)
            gender = st.selectbox("Gender Identity", 
                                 ["Male", "Female", "Non-binary", "Transgender male", "Transgender female", "Other", "Prefer not to disclose"])
            sex_assigned = st.selectbox("Sex Assigned at Birth", ["Male", "Female", "Intersex", "Prefer not to disclose"])
            
            marital_status = st.selectbox("Marital Status", 
                                        ["Single, never married", "Married", "Divorced", "Widowed", 
                                         "Separated", "Cohabiting/Domestic partnership", "Other"])
            
            children = st.selectbox("Children", ["None", "1", "2", "3", "4+"])
            
        with col2:
            st.markdown('<div class="subsection-header">Socioeconomic Status</div>', unsafe_allow_html=True)
            education = st.selectbox("Highest Education Level", 
                                   ["Less than high school", "High school/GED", "Some college", 
                                    "Associate degree", "Bachelor's degree", "Master's degree", 
                                    "Doctoral degree", "Professional degree"])
            
            occupation = st.text_input("Current/Most Recent Occupation")
            employment_status = st.selectbox("Employment Status", 
                                           ["Employed full-time", "Employed part-time", "Unemployed", 
                                            "Student", "Retired", "Disabled/Unable to work", "Homemaker"])
            
            # Removed income level and insurance as non-essential for ML
            
        with col3:
            st.markdown('<div class="subsection-header">Cultural & Background</div>', unsafe_allow_html=True)
            with st.form("ethnicity_form"):
                ethnicity = st.multiselect("Race/Ethnicity (select all that apply)", 
                                         ["White/Caucasian", "Black/African American", "Hispanic/Latino", 
                                          "Asian", "Native American/Alaska Native", "Pacific Islander", 
                                          "Middle Eastern", "Mixed race", "Other"],
                                         default=st.session_state.patient_data.get('demographics', {}).get('ethnicity', []))
                
                ethnicity_submitted = st.form_submit_button("💾 Update Ethnicity", use_container_width=True)
                
                if ethnicity_submitted:
                    if 'demographics' not in st.session_state.patient_data:
                        st.session_state.patient_data['demographics'] = {}
                    st.session_state.patient_data['demographics']['ethnicity'] = ethnicity
                    st.success("✅ Ethnicity updated successfully!")
            
            # ... existing code ...
            
            living_situation = st.selectbox("Current Living Situation", 
                                          ["Lives alone", "With spouse/partner", "With family", "With roommates",
                                           "Assisted living", "Group home", "Homeless", "Incarcerated", "Other"])
            
            housing_stability = st.selectbox("Housing Stability", 
                                           ["Stable housing", "Temporary housing", "Frequent moves", "Homeless"])
        
        # Emergency contacts
        st.markdown('<div class="subsection-header">Emergency Contact Information</div>', unsafe_allow_html=True)
        col1, col2 = st.columns(2)
        with col1:
            emergency_contact_name = st.text_input("Emergency Contact Name")
            emergency_contact_relationship = st.text_input("Relationship to Patient")
        with col2:
            emergency_contact_phone = st.text_input("Emergency Contact Phone")
            emergency_contact_address = st.text_area("Emergency Contact Address", height=100)
        
        # Validation before storing (but always store data)
        errors = []
        # Only validate age if it's provided and check range manually
        if age is not None and (age < 1 or age > 120):
            errors.append("Age must be between 1 and 120")
        # Emergency contact is optional
        if emergency_contact_name and not emergency_contact_phone:
            errors.append("Emergency contact phone is required if name is provided")
        
        # Store validation errors if any
        if errors:
            st.session_state.validation_errors['demographics_errors'] = errors
        else:
            if 'demographics_errors' in st.session_state.validation_errors:
                del st.session_state.validation_errors['demographics_errors']
        
        # Always store data regardless of validation errors
        st.session_state.patient_data['demographics'] = {
            'age': age, 'gender': gender, 'sex_assigned': sex_assigned, 'marital_status': marital_status,
            'children': children, 'education': education, 'occupation': occupation, 
            'employment_status': employment_status,
            'ethnicity': ethnicity, 'living_situation': living_situation, 'housing_stability': housing_stability,
            'emergency_contact': {
                'name': emergency_contact_name, 'relationship': emergency_contact_relationship,
                'phone': emergency_contact_phone, 'address': emergency_contact_address
            }
        }

    # Section 1: Chief Complaint & Referral
    elif st.session_state.current_section == 1:
        st.markdown('<div class="section-header">Chief Complaint & Referral</div>', unsafe_allow_html=True)
        
        if 'chief_complaint' not in st.session_state.patient_data:
            st.session_state.patient_data['chief_complaint'] = {}
        
        chief_complaint = st.session_state.patient_data['chief_complaint']
        
        # Common Chief Complaints
        st.markdown("### 🎯 Common Chief Complaints (Select all that apply)")
        st.info("💡 **Tip:** Use the form below to select multiple options without the dropdown closing.")
        
        with st.form("chief_complaints_form"):
            common_complaints = st.multiselect(
                "Select Common Complaints",
                ["Depression", "Anxiety", "Panic attacks", "Mood swings", "Sleep problems", 
                 "Concentration problems", "Memory problems", "Hearing voices", "Paranoid thoughts", 
                 "Suicidal thoughts", "Self-harm", "Substance use problems", "Relationship problems", 
                 "Work/school stress", "Grief/loss", "Trauma symptoms", "Eating problems", 
                 "Anger management", "Social anxiety", "Phobias", "Obsessive thoughts", 
                 "Compulsive behaviors", "Medication management", "Second opinion"],
                default=chief_complaint.get('common_complaints', [])
            )
            
            complaints_submitted = st.form_submit_button("💾 Update Chief Complaints", use_container_width=True)
            
            if complaints_submitted:
                chief_complaint['common_complaints'] = common_complaints
                st.success("✅ Chief complaints updated successfully!")
                
                if st.session_state.get('auto_save_enabled', True):
                    try:
                        save_assessment_data(st.session_state.patient_data)
                        st.info("💾 Auto-saved to database")
                    except Exception as e:
                        st.warning(f"Auto-save failed: {str(e)}")
        
        # Patient's Own Words
        chief_complaint['presenting_problem'] = st.text_area(
            "Chief Complaint (in patient's own words)",
            value=chief_complaint.get('presenting_problem', ''),
            height=100,
            help="What brought the patient to seek help today? Use patient's exact words when possible."
        )
        
        # Timeline and Context
        st.markdown("### ⏰ Timeline and Context")
        col1, col2, col3 = st.columns(3)
        
        with col1:
            chief_complaint['duration'] = st.selectbox(
                "Duration of Current Problem",
                ["", "Hours", "Days", "1-2 weeks", "2-4 weeks", "1-3 months", "3-6 months", "6-12 months", "1-2 years", "More than 2 years"],
                index=0 if not chief_complaint.get('duration') else ["", "Hours", "Days", "1-2 weeks", "2-4 weeks", "1-3 months", "3-6 months", "6-12 months", "1-2 years", "More than 2 years"].index(chief_complaint.get('duration'))
            )
            
            chief_complaint['onset_type'] = st.selectbox(
                "Onset Type",
                ["", "Gradual", "Sudden", "Following specific event", "Seasonal pattern", "Unknown"],
                index=0 if not chief_complaint.get('onset_type') else ["", "Gradual", "Sudden", "Following specific event", "Seasonal pattern", "Unknown"].index(chief_complaint.get('onset_type'))
            )
        
        with col2:
            chief_complaint['severity_change'] = st.selectbox(
                "Severity Over Time",
                ["", "Getting worse", "Getting better", "Staying the same", "Fluctuating", "Episodic"],
                index=0 if not chief_complaint.get('severity_change') else ["", "Getting worse", "Getting better", "Staying the same", "Fluctuating", "Episodic"].index(chief_complaint.get('severity_change'))
            )
            
            chief_complaint['current_severity'] = st.selectbox(
                "Current Severity (1-10 scale)",
                ["", "1-2 (Mild)", "3-4 (Mild-Moderate)", "5-6 (Moderate)", "7-8 (Severe)", "9-10 (Extreme)"],
                index=0 if not chief_complaint.get('current_severity') else ["", "1-2 (Mild)", "3-4 (Mild-Moderate)", "5-6 (Moderate)", "7-8 (Severe)", "9-10 (Extreme)"].index(chief_complaint.get('current_severity'))
            )
        
        with col3:
            chief_complaint['urgency'] = st.selectbox(
                "Urgency Level",
                ["", "Routine", "Urgent", "Emergent", "Crisis"],
                index=0 if not chief_complaint.get('urgency') else ["", "Routine", "Urgent", "Emergent", "Crisis"].index(chief_complaint.get('urgency'))
            )
            
            chief_complaint['previous_episodes'] = st.selectbox(
                "Previous Similar Episodes",
                ["", "First episode", "Second episode", "Multiple episodes", "Chronic/ongoing"],
                index=0 if not chief_complaint.get('previous_episodes') else ["", "First episode", "Second episode", "Multiple episodes", "Chronic/ongoing"].index(chief_complaint.get('previous_episodes'))
            )
        
        # Referral Information
        st.markdown("### 📋 Referral Information")
        col1, col2 = st.columns(2)
        
        with col1:
            chief_complaint['referral_source'] = st.selectbox(
                "Referral Source",
                ["", "Self-referral", "Family member", "Friend", "Primary care physician", "Emergency department", 
                 "Psychiatrist", "Psychologist", "Therapist/counselor", "Social worker", "Court/legal system", 
                 "School counselor", "Employee assistance program", "Insurance company", "Online/telehealth", "Other"],
                index=0 if not chief_complaint.get('referral_source') else ["", "Self-referral", "Family member", "Friend", "Primary care physician", "Emergency department", "Psychiatrist", "Psychologist", "Therapist/counselor", "Social worker", "Court/legal system", "School counselor", "Employee assistance program", "Insurance company", "Online/telehealth", "Other"].index(chief_complaint.get('referral_source'))
            )
            
            with st.form("referral_reason_form"):
                referral_reason = st.multiselect(
                    "Reason for Referral",
                    ["Diagnostic evaluation", "Medication management", "Therapy/counseling", "Second opinion", 
                     "Crisis intervention", "Substance abuse treatment", "Specialized treatment", "Court-ordered evaluation", 
                     "Disability evaluation", "Treatment resistance", "Side effects management", "Suicidal ideation", 
                     "Psychotic symptoms", "Mood stabilization", "Anxiety management"],
                    default=chief_complaint.get('referral_reason', [])
                )
                
                referral_reason_submitted = st.form_submit_button("💾 Update Referral Reason", use_container_width=True)
                
                if referral_reason_submitted:
                    chief_complaint['referral_reason'] = referral_reason
                    st.success("✅ Referral reason updated successfully!")
                    
                    if st.session_state.get('auto_save_enabled', True):
                        try:
                            save_assessment_data(st.session_state.patient_data)
                            st.info("💾 Auto-saved to database")
                        except Exception as e:
                            st.warning(f"Auto-save failed: {str(e)}")
        
        with col2:
            with st.form("referral_treatment_form"):
                what_helps = st.multiselect(
                    "What Has Helped Before",
                    ["Nothing tried yet", "Medication", "Therapy/counseling", "Support groups", "Exercise", 
                     "Meditation/mindfulness", "Religious/spiritual practices", "Family support", "Time off work", 
                     "Lifestyle changes", "Alternative treatments", "Hospitalization", "Other"],
                    default=chief_complaint.get('what_helps', [])
                )
                
                what_makes_worse = st.multiselect(
                    "What Makes It Worse",
                    ["Stress", "Lack of sleep", "Substance use", "Conflict", "Work pressure", "Financial problems", 
                     "Relationship issues", "Medical problems", "Seasonal changes", "Isolation", "Certain medications", 
                     "Caffeine", "Alcohol", "Other"],
                    default=chief_complaint.get('what_makes_worse', [])
                )
                
                referral_submitted = st.form_submit_button("💾 Update Referral Info", use_container_width=True)
                
                if referral_submitted:
                    chief_complaint.update({
                        'what_helps': what_helps,
                        'what_makes_worse': what_makes_worse
                    })
                    st.success("✅ Referral information updated successfully!")
                    
                    if st.session_state.get('auto_save_enabled', True):
                        try:
                            save_assessment_data(st.session_state.patient_data)
                            st.info("💾 Auto-saved to database")
                        except Exception as e:
                            st.warning(f"Auto-save failed: {str(e)}")
        
        # Goals and Expectations
        st.markdown("### 🎯 Treatment Goals and Expectations")
        with st.form("treatment_goals_form"):
            treatment_goals = st.multiselect(
                "Patient's Treatment Goals",
                ["Feel less depressed", "Reduce anxiety", "Sleep better", "Improve concentration", "Better relationships", 
                 "Return to work/school", "Stop substance use", "Manage anger", "Cope with stress", "Medication adjustment", 
                 "Understand diagnosis", "Prevent hospitalization", "Improve self-esteem", "Handle trauma", "Other"],
                default=chief_complaint.get('treatment_goals', [])
            )
            
            expectations = st.text_area(
                "Patient's Expectations from Treatment",
                value=chief_complaint.get('expectations', ''),
                height=80,
                help="What does the patient hope to achieve?"
            )
            
            goals_submitted = st.form_submit_button("💾 Update Treatment Goals", use_container_width=True)
            
            if goals_submitted:
                chief_complaint.update({
                    'treatment_goals': treatment_goals,
                    'expectations': expectations
                })
                st.success("✅ Treatment goals updated successfully!")
                
                if st.session_state.get('auto_save_enabled', True):
                    try:
                        save_assessment_data(st.session_state.patient_data)
                        st.info("💾 Auto-saved to database")
                    except Exception as e:
                        st.warning(f"Auto-save failed: {str(e)}")
        
        st.session_state.patient_data['chief_complaint'] = chief_complaint

    # Section 2: History of Present Illness
    elif st.session_state.current_section == 2:
        st.markdown('<div class="section-header">History of Present Illness</div>', unsafe_allow_html=True)
        
        if 'history_present_illness' not in st.session_state.patient_data:
            st.session_state.patient_data['history_present_illness'] = {}
        
        hpi = st.session_state.patient_data['history_present_illness']
        
        # Use the new form-based approach for better user experience
        create_form_based_multiselect_section()
        
        # Continue with remaining sections using forms...
        # Psychotic Disorders Form
        with st.expander("🌀 Psychotic Disorders", expanded=False):
            with st.form("psychotic_disorders_form"):
                if 'psychotic_symptoms' not in hpi:
                    hpi['psychotic_symptoms'] = {}
                
                col1, col2 = st.columns(2)
                with col1:
                    positive_symptoms = st.multiselect(
                        "Positive Symptoms",
                        ["Auditory hallucinations", "Visual hallucinations", "Delusions of persecution", 
                         "Delusions of grandeur", "Delusions of reference", "Thought broadcasting", 
                         "Thought insertion", "Disorganized speech", "Bizarre behavior"],
                        default=hpi['psychotic_symptoms'].get('positive_symptoms', [])
                    )
                
                with col2:
                    negative_symptoms = st.multiselect(
                        "Negative Symptoms",
                        ["Avolition", "Alogia", "Anhedonia", "Affective flattening", 
                         "Social withdrawal", "Poor hygiene", "Lack of motivation"],
                        default=hpi['psychotic_symptoms'].get('negative_symptoms', [])
                    )
                
                psychotic_submitted = st.form_submit_button("💾 Update Psychotic Symptoms", use_container_width=True)
                
                if psychotic_submitted:
                    hpi['psychotic_symptoms'].update({
                        'positive_symptoms': positive_symptoms,
                        'negative_symptoms': negative_symptoms
                    })
                    st.success("✅ Psychotic symptoms updated successfully!")
                    
                    if st.session_state.get('auto_save_enabled', True):
                        try:
                            save_assessment_data(st.session_state.patient_data)
                            st.info("💾 Auto-saved to database")
                        except Exception as e:
                            st.warning(f"Auto-save failed: {str(e)}")
        
        # OCRD Form
        with st.expander("🔄 OCRD", expanded=False):
            with st.form("ocrd_form"):
                if 'ocd_symptoms' not in hpi:
                    hpi['ocd_symptoms'] = {}
                
                col1, col2 = st.columns(2)
                with col1:
                    obsessions = st.multiselect(
                        "Obsessions",
                        ["Contamination fears", "Symmetry/order", "Aggressive thoughts", 
                         "Sexual thoughts", "Religious/moral concerns", "Somatic concerns"],
                        default=hpi['ocd_symptoms'].get('obsessions', [])
                    )
                
                with col2:
                    compulsions = st.multiselect(
                        "Compulsions",
                        ["Washing/cleaning", "Checking", "Ordering/arranging", "Counting", 
                         "Repeating", "Mental rituals", "Reassurance seeking"],
                        default=hpi['ocd_symptoms'].get('compulsions', [])
                    )
                
                ocd_submitted = st.form_submit_button("💾 Update OCRD Symptoms", use_container_width=True)
                
                if ocd_submitted:
                    hpi['ocd_symptoms'].update({
                        'obsessions': obsessions,
                        'compulsions': compulsions
                    })
                    st.success("✅ OCRD symptoms updated successfully!")
                    
                    if st.session_state.get('auto_save_enabled', True):
                        try:
                            save_assessment_data(st.session_state.patient_data)
                            st.info("💾 Auto-saved to database")
                        except Exception as e:
                            st.warning(f"Auto-save failed: {str(e)}")
        
        # Trauma and Stress-Related Disorders Form
        with st.expander("⚡ Trauma and Stress-Related Disorders", expanded=False):
            with st.form("trauma_disorders_form"):
                if 'trauma_symptoms' not in hpi:
                    hpi['trauma_symptoms'] = {}
                
                col1, col2 = st.columns(2)
                with col1:
                    ptsd_symptoms = st.multiselect(
                        "PTSD Symptoms",
                        ["Intrusive memories", "Nightmares", "Flashbacks", "Avoidance of triggers", 
                         "Negative mood changes", "Hypervigilance", "Exaggerated startle", 
                         "Sleep problems", "Concentration problems"],
                        default=hpi['trauma_symptoms'].get('ptsd_symptoms', [])
                    )
                
                with col2:
                    acute_stress = st.multiselect(
                        "Acute Stress Symptoms",
                        ["Dissociative symptoms", "Re-experiencing", "Avoidance", 
                         "Negative mood", "Arousal symptoms"],
                        default=hpi['trauma_symptoms'].get('acute_stress', [])
                    )
                
                trauma_submitted = st.form_submit_button("💾 Update Trauma Symptoms", use_container_width=True)
                
                if trauma_submitted:
                    hpi['trauma_symptoms'].update({
                        'ptsd_symptoms': ptsd_symptoms,
                        'acute_stress': acute_stress
                    })
                    st.success("✅ Trauma symptoms updated successfully!")
                    
                    if st.session_state.get('auto_save_enabled', True):
                        try:
                            save_assessment_data(st.session_state.patient_data)
                            st.info("💾 Auto-saved to database")
                        except Exception as e:
                            st.warning(f"Auto-save failed: {str(e)}")
        
        # Psychotic Disorders
        with st.expander("🌀 Psychotic Disorders", expanded=False):
            if 'psychotic_symptoms' not in hpi:
                hpi['psychotic_symptoms'] = {}
            
            col1, col2 = st.columns(2)
            with col1:
                hpi['psychotic_symptoms']['positive_symptoms'] = st.multiselect(
                    "Positive Symptoms",
                    ["Auditory hallucinations", "Visual hallucinations", "Delusions of persecution", 
                     "Delusions of grandeur", "Delusions of reference", "Thought broadcasting", 
                     "Thought insertion", "Disorganized speech", "Bizarre behavior"],
                    default=hpi['psychotic_symptoms'].get('positive_symptoms', [])
                )
            
            with col2:
                hpi['psychotic_symptoms']['negative_symptoms'] = st.multiselect(
                    "Negative Symptoms",
                    ["Avolition", "Alogia", "Anhedonia", "Affective flattening", 
                     "Social withdrawal", "Poor hygiene", "Lack of motivation"],
                    default=hpi['psychotic_symptoms'].get('negative_symptoms', [])
                )
        

        # Eating Disorders
        with st.expander("🍽️ Eating Disorders", expanded=False):
            if 'eating_symptoms' not in hpi:
                hpi['eating_symptoms'] = {}
            
            hpi['eating_symptoms']['eating_disorder_symptoms'] = st.multiselect(
                "Eating Disorder Symptoms",
                ["Restriction of food intake", "Binge eating", "Compensatory behaviors", 
                 "Body image distortion", "Fear of weight gain", "Amenorrhea", 
                 "Preoccupation with food/weight", "Social eating avoidance"],
                default=hpi['eating_symptoms'].get('eating_disorder_symptoms', [])
            )
        
        # Substance Use Disorders
        with st.expander("💊 Substance Use Disorders", expanded=False):
            if 'substance_symptoms' not in hpi:
                hpi['substance_symptoms'] = {}
            
            hpi['substance_symptoms']['sud_symptoms'] = st.multiselect(
                "Substance Use Disorder Symptoms",
                ["Tolerance", "Withdrawal", "Using more than intended", "Unsuccessful quit attempts", 
                 "Time spent obtaining/using", "Social/occupational problems", "Continued use despite problems", 
                 "Craving", "Failure to fulfill obligations", "Hazardous use", "Legal problems"],
                default=hpi['substance_symptoms'].get('sud_symptoms', [])
            )
        
        # Timeline and Course
        st.markdown("### ⏰ Timeline and Course")
        col1, col2, col3 = st.columns(3)
        
        with col1:
            hpi['onset'] = st.selectbox(
                "Onset",
                ["", "Gradual (weeks-months)", "Sudden (days)", "Acute (hours)", "Insidious (years)"],
                index=0 if not hpi.get('onset') else ["", "Gradual (weeks-months)", "Sudden (days)", "Acute (hours)", "Insidious (years)"].index(hpi.get('onset'))
            )
            
            hpi['duration'] = st.selectbox(
                "Duration of Current Episode",
                ["", "Days", "Weeks", "Months", "Years", "Chronic (>2 years)"],
                index=0 if not hpi.get('duration') else ["", "Days", "Weeks", "Months", "Years", "Chronic (>2 years)"].index(hpi.get('duration'))
            )
        
        with col2:
            hpi['course'] = st.selectbox(
                "Course",
                ["", "Stable", "Improving", "Worsening", "Fluctuating", "Episodic"],
                index=0 if not hpi.get('course') else ["", "Stable", "Improving", "Worsening", "Fluctuating", "Episodic"].index(hpi.get('course'))
            )
            
            hpi['severity'] = st.selectbox(
                "Current Severity",
                ["", "Mild", "Moderate", "Severe", "Extreme"],
                index=0 if not hpi.get('severity') else ["", "Mild", "Moderate", "Severe", "Extreme"].index(hpi.get('severity'))
            )
        
        with col3:
            with st.form("functional_impairment_form"):
                functional_impairment = st.multiselect(
                    "Functional Impairment",
                    ["Work/school", "Relationships", "Self-care", "Social activities", 
                     "Physical health", "Financial", "Legal"],
                    default=hpi.get('functional_impairment', [])
                )
                
                impairment_submitted = st.form_submit_button("💾 Update Impairment", use_container_width=True)
                
                if impairment_submitted:
                    hpi['functional_impairment'] = functional_impairment
                    st.success("✅ Functional impairment updated successfully!")
                    
                    if st.session_state.get('auto_save_enabled', True):
                        try:
                            save_assessment_data(st.session_state.patient_data)
                            st.info("💾 Auto-saved to database")
                        except Exception as e:
                            st.warning(f"Auto-save failed: {str(e)}")
        
        # Precipitating and Contributing Factors
        st.markdown("### 🎯 Contributing Factors")
        with st.form("contributing_factors_form"):
            col1, col2 = st.columns(2)
            
            with col1:
                precipitating_factors = st.multiselect(
                    "Precipitating Factors",
                    ["Life stressor", "Medical illness", "Medication change", "Substance use", 
                     "Sleep disruption", "Relationship problems", "Work stress", "Financial stress", 
                     "Loss/grief", "Trauma", "Other"],
                    default=hpi.get('precipitating_factors', [])
                )
            
            with col2:
                aggravating_factors = st.multiselect(
                    "Aggravating Factors",
                    ["Stress", "Lack of sleep", "Substance use", "Isolation", "Conflict", 
                     "Medical problems", "Medication non-compliance", "Seasonal changes"],
                    default=hpi.get('aggravating_factors', [])
                )
            
            factors_submitted = st.form_submit_button("💾 Update Contributing Factors", use_container_width=True)
            
            if factors_submitted:
                hpi.update({
                    'precipitating_factors': precipitating_factors,
                    'aggravating_factors': aggravating_factors
                })
                st.success("✅ Contributing factors updated successfully!")
                
                if st.session_state.get('auto_save_enabled', True):
                    try:
                        save_assessment_data(st.session_state.patient_data)
                        st.info("💾 Auto-saved to database")
                    except Exception as e:
                        st.warning(f"Auto-save failed: {str(e)}")
        
        # Additional narrative
        hpi['additional_details'] = st.text_area(
            "Additional Clinical Details",
            value=hpi.get('additional_details', ''),
            height=100,
            help="Any additional relevant information not captured above"
        )
        
        st.session_state.patient_data['history_present_illness'] = hpi

    # Section 3: Past Psychiatric History
    elif st.session_state.current_section == 3:
        # Render enhanced CSS
        render_enhanced_css()
        
        st.markdown('<div class="section-header">Past Psychiatric History</div>', unsafe_allow_html=True)
        
        # Add section progress indicator
        render_section_progress(completed_sections=2, current_section=3, total_sections=16)
        
        if 'past_psychiatric_history' not in st.session_state.patient_data:
            st.session_state.patient_data['past_psychiatric_history'] = {}
        
        pph = st.session_state.patient_data['past_psychiatric_history']
        
        # Previous Diagnoses
        st.markdown("### 🏷️ Previous Psychiatric Diagnoses")
        with st.form("previous_diagnoses_form"):
            previous_diagnoses = st.multiselect(
                "Previous Psychiatric Diagnoses",
                ["Major Depressive Disorder", "Bipolar I Disorder", "Bipolar II Disorder", "Persistent Depressive Disorder", 
                 "Generalized Anxiety Disorder", "Panic Disorder", "Social Anxiety Disorder", "Specific Phobia", 
                 "Obsessive-Compulsive Disorder", "PTSD", "Acute Stress Disorder", "Adjustment Disorder", 
                 "Schizophrenia", "Schizoaffective Disorder", "Brief Psychotic Disorder", "Delusional Disorder", 
                 "Substance Use Disorder", "Alcohol Use Disorder", "ADHD", "Autism Spectrum Disorder", 
                 "Anorexia Nervosa", "Bulimia Nervosa", "Binge Eating Disorder", "Borderline Personality Disorder", 
                 "Antisocial Personality Disorder", "Other Personality Disorder", "Intellectual Disability", 
                 "Dementia", "Other"],
                default=pph.get('previous_diagnoses', [])
            )
            
            diagnoses_submitted = st.form_submit_button("💾 Update Previous Diagnoses", use_container_width=True)
            
            if diagnoses_submitted:
                pph['previous_diagnoses'] = previous_diagnoses
                st.success("✅ Previous diagnoses updated successfully!")
                
                if st.session_state.get('auto_save_enabled', True):
                    try:
                        save_assessment_data(st.session_state.patient_data)
                        st.info("💾 Auto-saved to database")
                    except Exception as e:
                        st.warning(f"Auto-save failed: {str(e)}")
        
        # Treatment History
        st.markdown("### 🏥 Treatment History")
        col1, col2 = st.columns(2)
        
        with col1:
            pph['hospitalizations'] = st.selectbox(
                "Psychiatric Hospitalizations",
                ["", "None", "1 hospitalization", "2-3 hospitalizations", "4-5 hospitalizations", "More than 5 hospitalizations"],
                index=0 if not pph.get('hospitalizations') else ["", "None", "1 hospitalization", "2-3 hospitalizations", "4-5 hospitalizations", "More than 5 hospitalizations"].index(pph.get('hospitalizations'))
            )
            
            with st.form("hospitalization_reasons_form"):
                hospitalization_reasons = st.multiselect(
                    "Reasons for Hospitalization",
                    ["Suicidal ideation/attempt", "Homicidal ideation", "Psychosis", "Severe depression", 
                     "Mania/hypomania", "Substance intoxication", "Substance withdrawal", "Self-harm", 
                     "Inability to care for self", "Medication adjustment", "Other"],
                    default=pph.get('hospitalization_reasons', [])
                )
                
                hosp_reasons_submitted = st.form_submit_button("💾 Update Hospitalization Reasons", use_container_width=True)
                
                if hosp_reasons_submitted:
                    pph['hospitalization_reasons'] = hospitalization_reasons
                    st.success("✅ Hospitalization reasons updated successfully!")
                    
                    if st.session_state.get('auto_save_enabled', True):
                        try:
                            save_assessment_data(st.session_state.patient_data)
                            st.info("💾 Auto-saved to database")
                        except Exception as e:
                            st.warning(f"Auto-save failed: {str(e)}")
            
            pph['emergency_visits'] = st.selectbox(
                "Emergency Department Visits",
                ["", "None", "1-2 visits", "3-5 visits", "More than 5 visits"],
                index=0 if not pph.get('emergency_visits') else ["", "None", "1-2 visits", "3-5 visits", "More than 5 visits"].index(pph.get('emergency_visits'))
            )
        
        with col2:
            with st.form("outpatient_treatment_form"):
                outpatient_treatment = st.multiselect(
                    "Previous Outpatient Treatment",
                    ["Individual therapy", "Group therapy", "Family therapy", "Couples therapy", "Medication management", 
                     "Intensive outpatient program", "Partial hospitalization", "Day treatment", "Case management", 
                     "Peer support", "Support groups", "Other"],
                    default=pph.get('outpatient_treatment', [])
                )
                
                outpatient_submitted = st.form_submit_button("💾 Update Outpatient Treatment", use_container_width=True)
                
                if outpatient_submitted:
                    pph['outpatient_treatment'] = outpatient_treatment
                    st.success("✅ Outpatient treatment updated successfully!")
                    
                    if st.session_state.get('auto_save_enabled', True):
                        try:
                            save_assessment_data(st.session_state.patient_data)
                            st.info("💾 Auto-saved to database")
                        except Exception as e:
                            st.warning(f"Auto-save failed: {str(e)}")
            
            pph['therapy_types'] = st.multiselect(
                "Types of Therapy Received",
                ["Cognitive Behavioral Therapy (CBT)", "Dialectical Behavior Therapy (DBT)", 
                 "Psychodynamic therapy", "Interpersonal therapy", "EMDR", "Exposure therapy", 
                 "Acceptance and Commitment Therapy", "Mindfulness-based therapy", "Family therapy", 
                 "Group therapy", "Art/music therapy", "Other"],
                default=pph.get('therapy_types', [])
            )
            
            pph['treatment_response'] = st.selectbox(
                "Overall Response to Previous Treatment",
                ["", "Excellent response", "Good response", "Partial response", "Poor response", "No response", "Mixed results"],
                index=0 if not pph.get('treatment_response') else ["", "Excellent response", "Good response", "Partial response", "Poor response", "No response", "Mixed results"].index(pph.get('treatment_response'))
            )
        
        # Enhanced Comprehensive Medication History with Progressive Disclosure
        st.markdown("### 💊 Comprehensive Psychiatric Medication History")
        
        # Show medication trial summary if data exists
        if 'medication_history' in pph:
            all_trials = []
            for med_class, data in pph['medication_history'].items():
                if isinstance(data, dict) and 'trials' in data:
                    all_trials.extend(data['trials'])
            
            if all_trials:
                render_medication_trial_summary(all_trials)
        
        # Validation errors display with recovery options
        if 'medication_errors' in st.session_state.validation_errors and st.session_state.validation_errors['medication_errors']:
            st.error("⚠️ **Medication History Data Validation Issues Detected:**")
            for error in st.session_state.validation_errors['medication_errors']:
                st.write(f"• {error}")
            
            col1, col2, col3 = st.columns(3)
            with col1:
                if st.button("🔧 Recover Medication Data", help="Attempt to recover from last known good state"):
                    try:
                        recovered = recover_corrupted_data("medication")
                        if 'medication_history' in recovered:
                            if 'medication_history' not in pph:
                                pph['medication_history'] = {}
                            pph['medication_history'].update(recovered['medication_history'])
                            st.success("✅ Medication history data recovered successfully!")
                            st.rerun()
                        else:
                            st.warning("⚠️ No recoverable data found, initialized with clean structure")
                    except Exception as e:
                        st.error(f"❌ Recovery failed: {e}")
            
            with col2:
                if st.button("🗑️ Reset Medication Data", help="Clear all medication history and start fresh"):
                    pph['medication_history'] = {
                        med_class: {'trials': [], 'overall_class_response': ''} 
                        for med_class in PSYCHIATRIC_MEDICATION_CLASSES.keys()
                    }
                    st.success("✅ Medication history reset successfully!")
                    st.rerun()
            
            with col3:
                if st.button("🔄 Revalidate Medication Data", help="Run validation again on current data"):
                    validation_results = validate_comprehensive_assessment(st.session_state.patient_data)
                    st.session_state.validation_errors = validation_results
                    if not validation_results.get('medication_errors'):
                        st.success("✅ Validation passed!")
                    st.rerun()
        
        st.markdown("""
        <div class="clinical-guidance">
            <h4>📋 Documentation Guidelines</h4>
            <p>Document detailed medication trials including response, side effects, duration, and discontinuation reasons. 
            Focus on medications with significant clinical impact or unusual responses.</p>
        </div>
        """, unsafe_allow_html=True)
        
        # Migrate existing data if needed
        if 'antidepressants_tried' in pph and 'medications' in pph['antidepressants_tried']:
            pph = migrate_existing_medication_data(pph)
        
        # Initialize medication history structure
        if 'medication_history' not in pph:
            pph['medication_history'] = {}
        
        # Antidepressants - Enhanced with subclasses and improved UI
        with st.expander("💊 Antidepressants", expanded=False):
            st.markdown('<div class="medication-class">', unsafe_allow_html=True)
            render_medication_class_guidance("Antidepressants")
            if 'Antidepressants' not in pph['medication_history']:
                pph['medication_history']['Antidepressants'] = {'trials': [], 'overall_class_response': ''}
            
            antidep_data = pph['medication_history']['Antidepressants']
            
            # SSRIs
            st.markdown("#### SSRIs (Selective Serotonin Reuptake Inhibitors)")
            ssri_tried = st.multiselect(
                "SSRIs Previously Tried",
                PSYCHIATRIC_MEDICATION_CLASSES['Antidepressants']['SSRIs'],
                default=[trial['medication'] for trial in antidep_data['trials'] if trial['medication'] in PSYCHIATRIC_MEDICATION_CLASSES['Antidepressants']['SSRIs']]
            )
            
            # SNRIs
            st.markdown("#### SNRIs (Serotonin-Norepinephrine Reuptake Inhibitors)")
            snri_tried = st.multiselect(
                "SNRIs Previously Tried",
                PSYCHIATRIC_MEDICATION_CLASSES['Antidepressants']['SNRIs'],
                default=[trial['medication'] for trial in antidep_data['trials'] if trial['medication'] in PSYCHIATRIC_MEDICATION_CLASSES['Antidepressants']['SNRIs']]
            )
            
            # Atypical Antidepressants
            st.markdown("#### Atypical Antidepressants")
            atypical_tried = st.multiselect(
                "Atypical Antidepressants Previously Tried",
                PSYCHIATRIC_MEDICATION_CLASSES['Antidepressants']['Atypical'],
                default=[trial['medication'] for trial in antidep_data['trials'] if trial['medication'] in PSYCHIATRIC_MEDICATION_CLASSES['Antidepressants']['Atypical']]
            )
            
            # TCAs
            st.markdown("#### TCAs (Tricyclic Antidepressants)")
            tca_tried = st.multiselect(
                "TCAs Previously Tried",
                PSYCHIATRIC_MEDICATION_CLASSES['Antidepressants']['TCAs'],
                default=[trial['medication'] for trial in antidep_data['trials'] if trial['medication'] in PSYCHIATRIC_MEDICATION_CLASSES['Antidepressants']['TCAs']]
            )
            
            # MAOIs
            st.markdown("#### MAOIs (Monoamine Oxidase Inhibitors)")
            maoi_tried = st.multiselect(
                "MAOIs Previously Tried",
                PSYCHIATRIC_MEDICATION_CLASSES['Antidepressants']['MAOIs'],
                default=[trial['medication'] for trial in antidep_data['trials'] if trial['medication'] in PSYCHIATRIC_MEDICATION_CLASSES['Antidepressants']['MAOIs']]
            )
            
            # Combine all antidepressants
            all_antideps_tried = ssri_tried + snri_tried + atypical_tried + tca_tried + maoi_tried
            
            # Update trials list
            antidep_data['trials'] = []
            for med in all_antideps_tried:
                # Find existing trial data or create new
                existing_trial = next((trial for trial in antidep_data['trials'] if trial['medication'] == med), None)
                if not existing_trial:
                    trial = {
                        'medication': med,
                        'class': next((k for k, v in PSYCHIATRIC_MEDICATION_CLASSES['Antidepressants'].items() if med in v), 'Unknown'),
                        'response_rating': None,
                        'side_effects': [],
                        'discontinuation_reason': '',
                        'adherence_rating': None,
                        'duration_weeks': None,
                        'starting_dose': '',
                        'maximum_dose': ''
                    }
                    antidep_data['trials'].append(trial)
            
            if all_antideps_tried:
                antidep_data['overall_class_response'] = st.selectbox(
                    "Overall Response to Antidepressants",
                    ["", "Excellent", "Good", "Partial", "Poor", "No response", "Mixed results"],
                    index=0 if not antidep_data.get('overall_class_response') else ["", "Excellent", "Good", "Partial", "Poor", "No response", "Mixed results"].index(antidep_data.get('overall_class_response'))
                )
                
                # Detailed trial information for selected medication
                if len(all_antideps_tried) > 0:
                    selected_med = st.selectbox(
                        "Select medication for detailed trial information:",
                        all_antideps_tried
                    )
                    
                    if selected_med:
                        trial = next((t for t in antidep_data['trials'] if t['medication'] == selected_med), None)
                        if trial:
                            col1, col2, col3 = st.columns(3)
                            with col1:
                                trial['response_rating'] = st.selectbox(
                                    f"Response to {selected_med}",
                                    [None, 1, 2, 3, 4, 5],
                                    format_func=lambda x: "Not rated" if x is None else f"{x} - {'No response' if x==1 else 'Poor' if x==2 else 'Fair' if x==3 else 'Good' if x==4 else 'Excellent'}",
                                    index=0 if trial['response_rating'] is None else trial['response_rating']
                                )
                                
                                trial['duration_weeks'] = st.number_input(
                                    "Duration (weeks)",
                                    min_value=0, max_value=520,
                                    value=trial['duration_weeks'] if trial['duration_weeks'] is not None else 0
                                )
                            
                            with col2:
                                trial['side_effects'] = st.multiselect(
                                    "Side Effects",
                                    ["None", "Nausea", "Headache", "Dizziness", "Drowsiness", "Insomnia", "Weight gain", "Weight loss", "Sexual dysfunction", "Dry mouth", "Constipation", "Other"],
                                    default=trial.get('side_effects', [])
                                )
                                
                                trial['starting_dose'] = st.text_input(
                                    "Starting Dose",
                                    value=trial.get('starting_dose', '')
                                )
                            
                            with col3:
                                trial['discontinuation_reason'] = st.selectbox(
                                    "Discontinuation Reason",
                                    ["", "Lack of efficacy", "Side effects", "Patient preference", "Cost", "Completed trial", "Other"],
                                    index=0 if not trial.get('discontinuation_reason') else ["", "Lack of efficacy", "Side effects", "Patient preference", "Cost", "Completed trial", "Other"].index(trial.get('discontinuation_reason'))
                                )
                                
                                trial['adherence_rating'] = st.selectbox(
                                    "Adherence",
                                    [None, 1, 2, 3, 4, 5],
                                    format_func=lambda x: "Not rated" if x is None else f"{x} - {'Very poor' if x==1 else 'Poor' if x==2 else 'Fair' if x==3 else 'Good' if x==4 else 'Excellent'}",
                                    index=0 if trial['adherence_rating'] is None else trial['adherence_rating']
                                )
        
        # Mood Stabilizers - Enhanced with subclasses
        with st.expander("⚖️ Mood Stabilizers", expanded=False):
            if 'Mood_Stabilizers' not in pph['medication_history']:
                pph['medication_history']['Mood_Stabilizers'] = {'trials': [], 'overall_class_response': ''}
            
            mood_stab_data = pph['medication_history']['Mood_Stabilizers']
            
            # Lithium
            st.markdown("#### Lithium")
            lithium_tried = st.multiselect(
                "Lithium Preparations Previously Tried",
                PSYCHIATRIC_MEDICATION_CLASSES['Mood_Stabilizers']['Lithium'],
                default=[trial['medication'] for trial in mood_stab_data['trials'] if trial['medication'] in PSYCHIATRIC_MEDICATION_CLASSES['Mood_Stabilizers']['Lithium']]
            )
            
            # Anticonvulsants
            st.markdown("#### Anticonvulsants")
            anticonv_tried = st.multiselect(
                "Anticonvulsants Previously Tried",
                PSYCHIATRIC_MEDICATION_CLASSES['Mood_Stabilizers']['Anticonvulsants'],
                default=[trial['medication'] for trial in mood_stab_data['trials'] if trial['medication'] in PSYCHIATRIC_MEDICATION_CLASSES['Mood_Stabilizers']['Anticonvulsants']]
            )
            
            all_mood_stabs_tried = lithium_tried + anticonv_tried
            
            # Update trials list
            mood_stab_data['trials'] = []
            for med in all_mood_stabs_tried:
                trial = {
                    'medication': med,
                    'class': 'Lithium' if med in PSYCHIATRIC_MEDICATION_CLASSES['Mood_Stabilizers']['Lithium'] else 'Anticonvulsant',
                    'response_rating': None,
                    'side_effects': [],
                    'discontinuation_reason': '',
                    'adherence_rating': None,
                    'duration_weeks': None,
                    'blood_levels_monitored': False
                }
                mood_stab_data['trials'].append(trial)
            
            if all_mood_stabs_tried:
                mood_stab_data['overall_class_response'] = st.selectbox(
                    "Overall Response to Mood Stabilizers",
                    ["", "Excellent", "Good", "Partial", "Poor", "No response", "Mixed results"],
                    index=0 if not mood_stab_data.get('overall_class_response') else ["", "Excellent", "Good", "Partial", "Poor", "No response", "Mixed results"].index(mood_stab_data.get('overall_class_response'))
                )
        
        # Antipsychotics - Enhanced with subclasses
        with st.expander("🧠 Antipsychotics", expanded=False):
            if 'Antipsychotics' not in pph['medication_history']:
                pph['medication_history']['Antipsychotics'] = {'trials': [], 'overall_class_response': ''}
            
            antipsych_data = pph['medication_history']['Antipsychotics']
            
            # Atypical Antipsychotics
            st.markdown("#### Atypical (Second-Generation) Antipsychotics")
            atypical_antipsych_tried = st.multiselect(
                "Atypical Antipsychotics Previously Tried",
                PSYCHIATRIC_MEDICATION_CLASSES['Antipsychotics']['Atypical'],
                default=[trial['medication'] for trial in antipsych_data['trials'] if trial['medication'] in PSYCHIATRIC_MEDICATION_CLASSES['Antipsychotics']['Atypical']]
            )
            
            # Typical Antipsychotics
            st.markdown("#### Typical (First-Generation) Antipsychotics")
            typical_antipsych_tried = st.multiselect(
                "Typical Antipsychotics Previously Tried",
                PSYCHIATRIC_MEDICATION_CLASSES['Antipsychotics']['Typical'],
                default=[trial['medication'] for trial in antipsych_data['trials'] if trial['medication'] in PSYCHIATRIC_MEDICATION_CLASSES['Antipsychotics']['Typical']]
            )
            
            all_antipsychs_tried = atypical_antipsych_tried + typical_antipsych_tried
            
            # Update trials list
            antipsych_data['trials'] = []
            for med in all_antipsychs_tried:
                trial = {
                    'medication': med,
                    'class': 'Atypical' if med in PSYCHIATRIC_MEDICATION_CLASSES['Antipsychotics']['Atypical'] else 'Typical',
                    'response_rating': None,
                    'side_effects': [],
                    'discontinuation_reason': '',
                    'adherence_rating': None,
                    'duration_weeks': None,
                    'eps_symptoms': False,
                    'metabolic_monitoring': False
                }
                antipsych_data['trials'].append(trial)
            
            if all_antipsychs_tried:
                antipsych_data['overall_class_response'] = st.selectbox(
                    "Overall Response to Antipsychotics",
                    ["", "Excellent", "Good", "Partial", "Poor", "No response", "Mixed results"],
                    index=0 if not antipsych_data.get('overall_class_response') else ["", "Excellent", "Good", "Partial", "Poor", "No response", "Mixed results"].index(antipsych_data.get('overall_class_response'))
                )
        
        # Anxiolytics/Benzodiazepines - Enhanced with subclasses
        with st.expander("😴 Anxiolytics/Benzodiazepines", expanded=False):
            if 'Anxiolytics_Benzodiazepines' not in pph['medication_history']:
                pph['medication_history']['Anxiolytics_Benzodiazepines'] = {'trials': [], 'overall_class_response': ''}
            
            benzo_data = pph['medication_history']['Anxiolytics_Benzodiazepines']
            
            # Short-acting Benzodiazepines
            st.markdown("#### Short-acting Benzodiazepines")
            short_acting_tried = st.multiselect(
                "Short-acting Benzodiazepines Previously Tried",
                PSYCHIATRIC_MEDICATION_CLASSES['Anxiolytics_Benzodiazepines']['Short_acting'],
                default=[trial['medication'] for trial in benzo_data['trials'] if trial['medication'] in PSYCHIATRIC_MEDICATION_CLASSES['Anxiolytics_Benzodiazepines']['Short_acting']]
            )
            
            # Long-acting Benzodiazepines
            st.markdown("#### Long-acting Benzodiazepines")
            long_acting_tried = st.multiselect(
                "Long-acting Benzodiazepines Previously Tried",
                PSYCHIATRIC_MEDICATION_CLASSES['Anxiolytics_Benzodiazepines']['Long_acting'],
                default=[trial['medication'] for trial in benzo_data['trials'] if trial['medication'] in PSYCHIATRIC_MEDICATION_CLASSES['Anxiolytics_Benzodiazepines']['Long_acting']]
            )
            
            all_benzos_tried = short_acting_tried + long_acting_tried
            
            # Update trials list
            benzo_data['trials'] = []
            for med in all_benzos_tried:
                trial = {
                    'medication': med,
                    'class': 'Short-acting' if med in PSYCHIATRIC_MEDICATION_CLASSES['Anxiolytics_Benzodiazepines']['Short_acting'] else 'Long-acting',
                    'response_rating': None,
                    'side_effects': [],
                    'discontinuation_reason': '',
                    'adherence_rating': None,
                    'duration_weeks': None,
                    'dependence_issues': False,
                    'withdrawal_symptoms': False
                }
                benzo_data['trials'].append(trial)
            
            if all_benzos_tried:
                benzo_data['overall_class_response'] = st.selectbox(
                    "Overall Response to Benzodiazepines",
                    ["", "Excellent", "Good", "Partial", "Poor", "No response", "Mixed results"],
                    index=0 if not benzo_data.get('overall_class_response') else ["", "Excellent", "Good", "Partial", "Poor", "No response", "Mixed results"].index(benzo_data.get('overall_class_response'))
                )
                
                # Detailed trial information for selected benzodiazepine
                if len(all_benzos_tried) > 0:
                    selected_benzo = st.selectbox(
                        "Select benzodiazepine for detailed trial information:",
                        all_benzos_tried,
                        key="benzo_detail_select"
                    )
                    
                    if selected_benzo:
                        trial = next((t for t in benzo_data['trials'] if t['medication'] == selected_benzo), None)
                        if trial:
                            col1, col2, col3 = st.columns(3)
                            with col1:
                                trial['response_rating'] = st.selectbox(
                                    f"Response to {selected_benzo}",
                                    [None, 1, 2, 3, 4, 5],
                                    format_func=lambda x: "Not rated" if x is None else f"{x} - {'No response' if x==1 else 'Poor' if x==2 else 'Fair' if x==3 else 'Good' if x==4 else 'Excellent'}",
                                    index=0 if trial['response_rating'] is None else trial['response_rating'],
                                    key=f"benzo_response_{selected_benzo}"
                                )
                                
                                trial['dependence_issues'] = st.checkbox(
                                    "Dependence issues",
                                    value=trial.get('dependence_issues', False),
                                    key=f"benzo_dependence_{selected_benzo}"
                                )
                            
                            with col2:
                                trial['side_effects'] = st.multiselect(
                                    "Side Effects",
                                    ["None", "Drowsiness", "Dizziness", "Confusion", "Memory problems", "Dependence", "Withdrawal symptoms", "Falls", "Other"],
                                    default=trial.get('side_effects', []),
                                    key=f"benzo_side_effects_{selected_benzo}"
                                )
                                
                                trial['withdrawal_symptoms'] = st.checkbox(
                                    "Withdrawal symptoms",
                                    value=trial.get('withdrawal_symptoms', False),
                                    key=f"benzo_withdrawal_{selected_benzo}"
                                )
                            
                            with col3:
                                trial['discontinuation_reason'] = st.selectbox(
                                    "Discontinuation Reason",
                                    ["", "Lack of efficacy", "Side effects", "Dependence concerns", "Patient preference", "Completed trial", "Other"],
                                    index=0 if not trial.get('discontinuation_reason') else ["", "Lack of efficacy", "Side effects", "Dependence concerns", "Patient preference", "Completed trial", "Other"].index(trial.get('discontinuation_reason')),
                                    key=f"benzo_discontinuation_{selected_benzo}"
                                )
                                
                                trial['duration_weeks'] = st.number_input(
                                    "Duration (weeks)",
                                    min_value=0, max_value=520,
                                    value=trial['duration_weeks'] if trial['duration_weeks'] is not None else 0,
                                    key=f"benzo_duration_{selected_benzo}"
                                )
        
        # Stimulants (ADHD Medications)
        with st.expander("⚡ Stimulants (ADHD Medications)", expanded=False):
            if 'Stimulants' not in pph['medication_history']:
                pph['medication_history']['Stimulants'] = {'trials': [], 'overall_class_response': ''}
            
            stimulant_data = pph['medication_history']['Stimulants']
            
            st.markdown("#### ADHD Medications")
            adhd_meds_tried = st.multiselect(
                "ADHD Medications Previously Tried",
                PSYCHIATRIC_MEDICATION_CLASSES['Stimulants']['ADHD_medications'],
                default=[trial['medication'] for trial in stimulant_data['trials'] if trial['medication'] in PSYCHIATRIC_MEDICATION_CLASSES['Stimulants']['ADHD_medications']]
            )
            
            # Update trials list
            stimulant_data['trials'] = []
            for med in adhd_meds_tried:
                trial = {
                    'medication': med,
                    'class': 'ADHD_medication',
                    'response_rating': None,
                    'side_effects': [],
                    'discontinuation_reason': '',
                    'adherence_rating': None,
                    'duration_weeks': None,
                    'appetite_suppression': False,
                    'sleep_issues': False
                }
                stimulant_data['trials'].append(trial)
            
            if adhd_meds_tried:
                stimulant_data['overall_class_response'] = st.selectbox(
                    "Overall Response to ADHD Medications",
                    ["", "Excellent", "Good", "Partial", "Poor", "No response", "Mixed results"],
                    index=0 if not stimulant_data.get('overall_class_response') else ["", "Excellent", "Good", "Partial", "Poor", "No response", "Mixed results"].index(stimulant_data.get('overall_class_response'))
                )
                
                # Detailed trial information for selected ADHD medication
                if len(adhd_meds_tried) > 0:
                    selected_adhd = st.selectbox(
                        "Select ADHD medication for detailed trial information:",
                        adhd_meds_tried,
                        key="adhd_detail_select"
                    )
                    
                    if selected_adhd:
                        trial = next((t for t in stimulant_data['trials'] if t['medication'] == selected_adhd), None)
                        if trial:
                            col1, col2, col3 = st.columns(3)
                            with col1:
                                trial['response_rating'] = st.selectbox(
                                    f"Response to {selected_adhd}",
                                    [None, 1, 2, 3, 4, 5],
                                    format_func=lambda x: "Not rated" if x is None else f"{x} - {'No response' if x==1 else 'Poor' if x==2 else 'Fair' if x==3 else 'Good' if x==4 else 'Excellent'}",
                                    index=0 if trial['response_rating'] is None else trial['response_rating'],
                                    key=f"adhd_response_{selected_adhd}"
                                )
                                
                                trial['appetite_suppression'] = st.checkbox(
                                    "Appetite suppression",
                                    value=trial.get('appetite_suppression', False),
                                    key=f"adhd_appetite_{selected_adhd}"
                                )
                            
                            with col2:
                                trial['side_effects'] = st.multiselect(
                                    "Side Effects",
                                    ["None", "Appetite suppression", "Sleep problems", "Irritability", "Mood changes", "Headache", "Stomach upset", "Growth suppression", "Tics", "Other"],
                                    default=trial.get('side_effects', []),
                                    key=f"adhd_side_effects_{selected_adhd}"
                                )
                                
                                trial['sleep_issues'] = st.checkbox(
                                    "Sleep issues",
                                    value=trial.get('sleep_issues', False),
                                    key=f"adhd_sleep_{selected_adhd}"
                                )
                            
                            with col3:
                                trial['discontinuation_reason'] = st.selectbox(
                                    "Discontinuation Reason",
                                    ["", "Lack of efficacy", "Side effects", "Growth concerns", "Patient preference", "Completed trial", "Other"],
                                    index=0 if not trial.get('discontinuation_reason') else ["", "Lack of efficacy", "Side effects", "Growth concerns", "Patient preference", "Completed trial", "Other"].index(trial.get('discontinuation_reason')),
                                    key=f"adhd_discontinuation_{selected_adhd}"
                                )
                                
                                trial['duration_weeks'] = st.number_input(
                                    "Duration (weeks)",
                                    min_value=0, max_value=520,
                                    value=trial['duration_weeks'] if trial['duration_weeks'] is not None else 0,
                                    key=f"adhd_duration_{selected_adhd}"
                                )
        
        # Sleep Medications
        with st.expander("🌙 Sleep Medications", expanded=False):
            if 'Sleep_Medications' not in pph['medication_history']:
                pph['medication_history']['Sleep_Medications'] = {'trials': [], 'overall_class_response': ''}
            
            sleep_data = pph['medication_history']['Sleep_Medications']
            
            # Z-drugs
            st.markdown("#### Z-drugs (Non-benzodiazepine hypnotics)")
            z_drugs_tried = st.multiselect(
                "Z-drugs Previously Tried",
                PSYCHIATRIC_MEDICATION_CLASSES['Sleep_Medications']['Z_drugs'],
                default=[trial['medication'] for trial in sleep_data['trials'] if trial['medication'] in PSYCHIATRIC_MEDICATION_CLASSES['Sleep_Medications']['Z_drugs']]
            )
            
            # Other Sleep Medications
            st.markdown("#### Other Sleep Medications")
            other_sleep_tried = st.multiselect(
                "Other Sleep Medications Previously Tried",
                PSYCHIATRIC_MEDICATION_CLASSES['Sleep_Medications']['Other'],
                default=[trial['medication'] for trial in sleep_data['trials'] if trial['medication'] in PSYCHIATRIC_MEDICATION_CLASSES['Sleep_Medications']['Other']]
            )
            
            all_sleep_meds_tried = z_drugs_tried + other_sleep_tried
            
            # Update trials list
            sleep_data['trials'] = []
            for med in all_sleep_meds_tried:
                trial = {
                    'medication': med,
                    'class': 'Z-drug' if med in PSYCHIATRIC_MEDICATION_CLASSES['Sleep_Medications']['Z_drugs'] else 'Other',
                    'response_rating': None,
                    'side_effects': [],
                    'discontinuation_reason': '',
                    'adherence_rating': None,
                    'duration_weeks': None,
                    'morning_grogginess': False,
                    'sleep_quality_improvement': False
                }
                sleep_data['trials'].append(trial)
            
            if all_sleep_meds_tried:
                sleep_data['overall_class_response'] = st.selectbox(
                    "Overall Response to Sleep Medications",
                    ["", "Excellent", "Good", "Partial", "Poor", "No response", "Mixed results"],
                    index=0 if not sleep_data.get('overall_class_response') else ["", "Excellent", "Good", "Partial", "Poor", "No response", "Mixed results"].index(sleep_data.get('overall_class_response'))
                )
                
                # Detailed trial information for selected sleep medication
                if len(all_sleep_meds_tried) > 0:
                    selected_sleep = st.selectbox(
                        "Select sleep medication for detailed trial information:",
                        all_sleep_meds_tried,
                        key="sleep_detail_select"
                    )
                    
                    if selected_sleep:
                        trial = next((t for t in sleep_data['trials'] if t['medication'] == selected_sleep), None)
                        if trial:
                            col1, col2, col3 = st.columns(3)
                            with col1:
                                trial['response_rating'] = st.selectbox(
                                    f"Response to {selected_sleep}",
                                    [None, 1, 2, 3, 4, 5],
                                    format_func=lambda x: "Not rated" if x is None else f"{x} - {'No response' if x==1 else 'Poor' if x==2 else 'Fair' if x==3 else 'Good' if x==4 else 'Excellent'}",
                                    index=0 if trial['response_rating'] is None else trial['response_rating'],
                                    key=f"sleep_response_{selected_sleep}"
                                )
                                
                                trial['sleep_quality_improvement'] = st.checkbox(
                                    "Sleep quality improvement",
                                    value=trial.get('sleep_quality_improvement', False),
                                    key=f"sleep_quality_{selected_sleep}"
                                )
                            
                            with col2:
                                trial['side_effects'] = st.multiselect(
                                    "Side Effects",
                                    ["None", "Morning grogginess", "Dizziness", "Headache", "Nausea", "Memory problems", "Dependence", "Rebound insomnia", "Other"],
                                    default=trial.get('side_effects', []),
                                    key=f"sleep_side_effects_{selected_sleep}"
                                )
                                
                                trial['morning_grogginess'] = st.checkbox(
                                    "Morning grogginess",
                                    value=trial.get('morning_grogginess', False),
                                    key=f"sleep_grogginess_{selected_sleep}"
                                )
                            
                            with col3:
                                trial['discontinuation_reason'] = st.selectbox(
                                    "Discontinuation Reason",
                                    ["", "Lack of efficacy", "Side effects", "Dependence concerns", "Patient preference", "Completed trial", "Other"],
                                    index=0 if not trial.get('discontinuation_reason') else ["", "Lack of efficacy", "Side effects", "Dependence concerns", "Patient preference", "Completed trial", "Other"].index(trial.get('discontinuation_reason')),
                                    key=f"sleep_discontinuation_{selected_sleep}"
                                )
                                
                                trial['duration_weeks'] = st.number_input(
                                    "Duration (weeks)",
                                    min_value=0, max_value=520,
                                    value=trial['duration_weeks'] if trial['duration_weeks'] is not None else 0,
                                    key=f"sleep_duration_{selected_sleep}"
                                )
        
        # Anti-anxiety Non-Benzodiazepine Medications
        with st.expander("🧘 Anti-anxiety (Non-Benzodiazepine)", expanded=False):
            if 'Anti_anxiety_Non_Benzo' not in pph['medication_history']:
                pph['medication_history']['Anti_anxiety_Non_Benzo'] = {'trials': [], 'overall_class_response': ''}
            
            non_benzo_data = pph['medication_history']['Anti_anxiety_Non_Benzo']
            
            st.markdown("#### Non-Benzodiazepine Anti-anxiety Medications")
            non_benzo_tried = st.multiselect(
                "Non-Benzodiazepine Anti-anxiety Medications Previously Tried",
                PSYCHIATRIC_MEDICATION_CLASSES['Anti_anxiety_Non_Benzo']['medications'],
                default=[trial['medication'] for trial in non_benzo_data['trials'] if trial['medication'] in PSYCHIATRIC_MEDICATION_CLASSES['Anti_anxiety_Non_Benzo']['medications']]
            )
            
            # Update trials list
            non_benzo_data['trials'] = []
            for med in non_benzo_tried:
                trial = {
                    'medication': med,
                    'class': 'Non-benzodiazepine',
                    'response_rating': None,
                    'side_effects': [],
                    'discontinuation_reason': '',
                    'adherence_rating': None,
                    'duration_weeks': None,
                    'onset_of_action': ''
                }
                non_benzo_data['trials'].append(trial)
            
            if non_benzo_tried:
                non_benzo_data['overall_class_response'] = st.selectbox(
                    "Overall Response to Non-Benzodiazepine Anti-anxiety Medications",
                    ["", "Excellent", "Good", "Partial", "Poor", "No response", "Mixed results"],
                    index=0 if not non_benzo_data.get('overall_class_response') else ["", "Excellent", "Good", "Partial", "Poor", "No response", "Mixed results"].index(non_benzo_data.get('overall_class_response'))
                )
                
                # Detailed trial information for selected non-benzo medication
                if len(non_benzo_tried) > 0:
                    selected_non_benzo = st.selectbox(
                        "Select medication for detailed trial information:",
                        non_benzo_tried,
                        key="non_benzo_detail_select"
                    )
                    
                    if selected_non_benzo:
                        trial = next((t for t in non_benzo_data['trials'] if t['medication'] == selected_non_benzo), None)
                        if trial:
                            col1, col2, col3 = st.columns(3)
                            with col1:
                                trial['response_rating'] = st.selectbox(
                                    f"Response to {selected_non_benzo}",
                                    [None, 1, 2, 3, 4, 5],
                                    format_func=lambda x: "Not rated" if x is None else f"{x} - {'No response' if x==1 else 'Poor' if x==2 else 'Fair' if x==3 else 'Good' if x==4 else 'Excellent'}",
                                    index=0 if trial['response_rating'] is None else trial['response_rating'],
                                    key=f"non_benzo_response_{selected_non_benzo}"
                                )
                                
                                trial['onset_of_action'] = st.selectbox(
                                    "Onset of action",
                                    ["", "Immediate", "Days", "Weeks", "Months"],
                                    index=0 if not trial.get('onset_of_action') else ["", "Immediate", "Days", "Weeks", "Months"].index(trial.get('onset_of_action')),
                                    key=f"non_benzo_onset_{selected_non_benzo}"
                                )
                            
                            with col2:
                                trial['side_effects'] = st.multiselect(
                                    "Side Effects",
                                    ["None", "Dizziness", "Nausea", "Headache", "Drowsiness", "Dry mouth", "Blurred vision", "Constipation", "Other"],
                                    default=trial.get('side_effects', []),
                                    key=f"non_benzo_side_effects_{selected_non_benzo}"
                                )
                                
                                trial['duration_weeks'] = st.number_input(
                                    "Duration (weeks)",
                                    min_value=0, max_value=520,
                                    value=trial['duration_weeks'] if trial['duration_weeks'] is not None else 0,
                                    key=f"non_benzo_duration_{selected_non_benzo}"
                                )
                            
                            with col3:
                                trial['discontinuation_reason'] = st.selectbox(
                                    "Discontinuation Reason",
                                    ["", "Lack of efficacy", "Side effects", "Patient preference", "Cost", "Completed trial", "Other"],
                                    index=0 if not trial.get('discontinuation_reason') else ["", "Lack of efficacy", "Side effects", "Patient preference", "Cost", "Completed trial", "Other"].index(trial.get('discontinuation_reason')),
                                    key=f"non_benzo_discontinuation_{selected_non_benzo}"
                                )
                                
                                trial['adherence_rating'] = st.selectbox(
                                    "Adherence",
                                    [None, 1, 2, 3, 4, 5],
                                    format_func=lambda x: "Not rated" if x is None else f"{x} - {'Very poor' if x==1 else 'Poor' if x==2 else 'Fair' if x==3 else 'Good' if x==4 else 'Excellent'}",
                                    index=0 if trial['adherence_rating'] is None else trial['adherence_rating'],
                                    key=f"non_benzo_adherence_{selected_non_benzo}"
                                )
        
        # Other Psychiatric Medications
        with st.expander("🔬 Other Psychiatric Medications", expanded=False):
            if 'Other_Psychiatric' not in pph['medication_history']:
                pph['medication_history']['Other_Psychiatric'] = {'trials': [], 'overall_class_response': ''}
            
            other_psych_data = pph['medication_history']['Other_Psychiatric']
            
            st.markdown("#### Other Psychiatric Medications")
            other_psych_tried = st.multiselect(
                "Other Psychiatric Medications Previously Tried",
                PSYCHIATRIC_MEDICATION_CLASSES['Other_Psychiatric']['medications'],
                default=[trial['medication'] for trial in other_psych_data['trials'] if trial['medication'] in PSYCHIATRIC_MEDICATION_CLASSES['Other_Psychiatric']['medications']]
            )
            
            # Update trials list
            other_psych_data['trials'] = []
            for med in other_psych_tried:
                trial = {
                    'medication': med,
                    'class': 'Other_psychiatric',
                    'response_rating': None,
                    'side_effects': [],
                    'discontinuation_reason': '',
                    'adherence_rating': None,
                    'duration_weeks': None,
                    'indication': ''
                }
                other_psych_data['trials'].append(trial)
            
            if other_psych_tried:
                other_psych_data['overall_class_response'] = st.selectbox(
                    "Overall Response to Other Psychiatric Medications",
                    ["", "Excellent", "Good", "Partial", "Poor", "No response", "Mixed results"],
                    index=0 if not other_psych_data.get('overall_class_response') else ["", "Excellent", "Good", "Partial", "Poor", "No response", "Mixed results"].index(other_psych_data.get('overall_class_response'))
                )
                
                # Detailed trial information for selected other psychiatric medication
                if len(other_psych_tried) > 0:
                    selected_other = st.selectbox(
                        "Select medication for detailed trial information:",
                        other_psych_tried,
                        key="other_psych_detail_select"
                    )
                    
                    if selected_other:
                        trial = next((t for t in other_psych_data['trials'] if t['medication'] == selected_other), None)
                        if trial:
                            col1, col2, col3 = st.columns(3)
                            with col1:
                                trial['response_rating'] = st.selectbox(
                                    f"Response to {selected_other}",
                                    [None, 1, 2, 3, 4, 5],
                                    format_func=lambda x: "Not rated" if x is None else f"{x} - {'No response' if x==1 else 'Poor' if x==2 else 'Fair' if x==3 else 'Good' if x==4 else 'Excellent'}",
                                    index=0 if trial['response_rating'] is None else trial['response_rating'],
                                    key=f"other_psych_response_{selected_other}"
                                )
                                
                                trial['indication'] = st.selectbox(
                                    "Indication",
                                    ["", "Substance use disorder", "Cognitive enhancement", "Sleep disorder", "Other"],
                                    index=0 if not trial.get('indication') else ["", "Substance use disorder", "Cognitive enhancement", "Sleep disorder", "Other"].index(trial.get('indication')),
                                    key=f"other_psych_indication_{selected_other}"
                                )
                            
                            with col2:
                                trial['side_effects'] = st.multiselect(
                                    "Side Effects",
                                    ["None", "Nausea", "Headache", "Dizziness", "Fatigue", "GI upset", "Sleep changes", "Mood changes", "Other"],
                                    default=trial.get('side_effects', []),
                                    key=f"other_psych_side_effects_{selected_other}"
                                )
                                
                                trial['duration_weeks'] = st.number_input(
                                    "Duration (weeks)",
                                    min_value=0, max_value=520,
                                    value=trial['duration_weeks'] if trial['duration_weeks'] is not None else 0,
                                    key=f"other_psych_duration_{selected_other}"
                                )
                            
                            with col3:
                                trial['discontinuation_reason'] = st.selectbox(
                                    "Discontinuation Reason",
                                    ["", "Lack of efficacy", "Side effects", "Patient preference", "Cost", "Completed trial", "Other"],
                                    index=0 if not trial.get('discontinuation_reason') else ["", "Lack of efficacy", "Side effects", "Patient preference", "Cost", "Completed trial", "Other"].index(trial.get('discontinuation_reason')),
                                    key=f"other_psych_discontinuation_{selected_other}"
                                )
                                
                                trial['adherence_rating'] = st.selectbox(
                                    "Adherence",
                                    [None, 1, 2, 3, 4, 5],
                                    format_func=lambda x: "Not rated" if x is None else f"{x} - {'Very poor' if x==1 else 'Poor' if x==2 else 'Fair' if x==3 else 'Good' if x==4 else 'Excellent'}",
                                    index=0 if trial['adherence_rating'] is None else trial['adherence_rating'],
                                    key=f"other_psych_adherence_{selected_other}"
                                )
        
        # Medication Allergies and Preferences
        st.markdown("### ⚠️ Medication Allergies and Preferences")
        col1, col2 = st.columns(2)
        
        with col1:
            if 'medication_allergies' not in pph:
                pph['medication_allergies'] = []
            
            pph['medication_allergies'] = st.multiselect(
                "Medication Allergies",
                ["None known", "Penicillin", "Sulfa drugs", "NSAIDs", "Codeine", "Morphine", "Latex", "Other"],
                default=pph.get('medication_allergies', [])
            )
            
            if 'Other' in pph.get('medication_allergies', []):
                pph['other_allergies'] = st.text_area(
                    "Describe Other Allergies",
                    value=pph.get('other_allergies', ''),
                    height=80
                )
        
        with col2:
            if 'medication_preferences' not in pph:
                pph['medication_preferences'] = {}
            
            pph['medication_preferences']['preferred_formulations'] = st.multiselect(
                "Preferred Medication Formulations",
                ["Tablets", "Capsules", "Liquid", "Patches", "Injections", "Sublingual", "No preference"],
                default=pph['medication_preferences'].get('preferred_formulations', [])
            )
            
            pph['medication_preferences']['barriers_to_adherence'] = st.multiselect(
                "Barriers to Medication Adherence",
                ["None", "Cost", "Side effects", "Forgetfulness", "Complex regimen", "Stigma", "Lack of efficacy", "Other"],
                default=pph['medication_preferences'].get('barriers_to_adherence', [])
            )
        
        # Enhanced medication validation with cross-section checks
        st.markdown("### 🔍 Medication Data Validation")
        
        if 'medication_history' in pph:
            # Create temporary assessment data for comprehensive validation
            temp_assessment_data = {
                'substance_use': st.session_state.patient_data.get('substance_use', {}),
                'past_psychiatric_history': {'medication_history': pph['medication_history']}
            }
            
            validation_results = perform_comprehensive_validation(temp_assessment_data)
            
            # Display validation results
            has_errors = any(errors for errors in validation_results.values())
            
            if has_errors:
                st.error("⚠️ Medication validation warnings detected:")
                for error_type, errors in validation_results.items():
                    if errors and error_type in ['medication_errors', 'medication_consistency_errors', 'cross_section_errors']:
                        st.error(f"**{error_type.replace('_', ' ').title()}:**")
                        for error in errors:
                            st.error(f"• {error}")
            else:
                st.success("✅ All medication validation checks passed")
            
            # Cross-section validation with substance use
            if 'substance_use' in st.session_state.patient_data:
                substance_data = st.session_state.patient_data['substance_use']
                
                # Check for medication interactions with substance use
                if substance_data.get('substances', {}).get('Alcohol', {}).get('used'):
                    alcohol_interacting_meds = []
                    for med_class, data in pph['medication_history'].items():
                        if 'trials' in data:
                            for trial in data['trials']:
                                if trial.get('medication') in ['Lithium', 'Valproic acid (Depakote)', 'Carbamazepine (Tegretol)']:
                                    alcohol_interacting_meds.append(trial['medication'])
                    
                    if alcohol_interacting_meds:
                        st.warning(f"⚠️ Cross-validation warning: Alcohol use documented with medications that have significant interactions: {', '.join(alcohol_interacting_meds)}")

        # Substance Use History
        st.markdown("### 🍷 Substance Use History")
        st.markdown("""
        <div class="info-box">
        📋 <strong>Clinical Note:</strong> This section captures historical substance use patterns relevant to psychiatric treatment.
        For comprehensive DSM-5 substance use disorder assessment, see Section 8: Substance Use Assessment.
        </div>
        """, unsafe_allow_html=True)

        if 'substance_history' not in pph:
            pph['substance_history'] = {}

        col1, col2 = st.columns(2)

        with col1:
            st.markdown("#### 🔍 Historical Use Patterns")
            pph['substance_history']['alcohol_history'] = st.selectbox(
                "Alcohol Use History",
                ["", "Never used", "Past use only", "Occasional social use", "Regular use", "Heavy use/dependence"],
                index=0 if not pph['substance_history'].get('alcohol_history') else
                ["", "Never used", "Past use only", "Occasional social use", "Regular use", "Heavy use/dependence"].index(pph['substance_history'].get('alcohol_history')),
                help="Historical pattern of alcohol use"
            )

            pph['substance_history']['illicit_drug_history'] = st.selectbox(
                "Illicit Drug Use History",
                ["", "Never used", "Experimental use only", "Past regular use", "Current use"],
                index=0 if not pph['substance_history'].get('illicit_drug_history') else
                ["", "Never used", "Experimental use only", "Past regular use", "Current use"].index(pph['substance_history'].get('illicit_drug_history')),
                help="Historical pattern of illicit drug use"
            )

            if pph['substance_history'].get('illicit_drug_history') and pph['substance_history']['illicit_drug_history'] not in ["", "Never used"]:
                pph['substance_history']['substances_used'] = st.multiselect(
                    "Substances Used (Historical)",
                    ["Cannabis", "Cocaine", "Methamphetamine", "Heroin", "Prescription opioids", "Benzodiazepines", "Hallucinogens", "Other"],
                    default=pph['substance_history'].get('substances_used', []),
                    help="Select all substances with significant historical use"
                )

        with col2:
            st.markdown("#### 🏥 Treatment & Impact")
            pph['substance_history']['substance_treatment'] = st.selectbox(
                "Previous Substance Use Treatment",
                ["", "None", "Outpatient counseling", "Intensive outpatient", "Inpatient detox", "Residential treatment", "12-step programs", "Multiple treatments"],
                index=0 if not pph['substance_history'].get('substance_treatment') else
                ["", "None", "Outpatient counseling", "Intensive outpatient", "Inpatient detox", "Residential treatment", "12-step programs", "Multiple treatments"].index(pph['substance_history'].get('substance_treatment')),
                help="Previous substance use treatment history"
            )

            pph['substance_history']['psychiatric_impact'] = st.selectbox(
                "Impact on Psychiatric Symptoms",
                ["", "No significant impact", "Worsened depression/anxiety", "Triggered psychotic symptoms", "Interfered with medication", "Led to hospitalization"],
                index=0 if not pph['substance_history'].get('psychiatric_impact') else
                ["", "No significant impact", "Worsened depression/anxiety", "Triggered psychotic symptoms", "Interfered with medication", "Led to hospitalization"].index(pph['substance_history'].get('psychiatric_impact')),
                help="How substance use affected psychiatric condition"
            )

            if pph['substance_history'].get('substance_treatment') and pph['substance_history']['substance_treatment'] not in ["", "None"]:
                pph['substance_history']['treatment_outcome'] = st.selectbox(
                    "Treatment Outcome",
                    ["", "Successful - sustained sobriety", "Partial success - reduced use", "Completed but relapsed", "Did not complete treatment"],
                    index=0 if not pph['substance_history'].get('treatment_outcome') else
                    ["", "Successful - sustained sobriety", "Partial success - reduced use", "Completed but relapsed", "Did not complete treatment"].index(pph['substance_history'].get('treatment_outcome')),
                    help="Outcome of previous substance use treatment"
                )

        # Self-harm and Suicide History
        st.markdown("### ⚠️ Self-harm and Suicide History")
        col1, col2 = st.columns(2)
        
        with col1:
            pph['suicide_attempts'] = st.selectbox(
                "Previous Suicide Attempts",
                ["", "None", "1 attempt", "2-3 attempts", "4-5 attempts", "More than 5 attempts"],
                index=0 if not pph.get('suicide_attempts') else ["", "None", "1 attempt", "2-3 attempts", "4-5 attempts", "More than 5 attempts"].index(pph.get('suicide_attempts'))
            )
            
            if pph.get('suicide_attempts') and pph['suicide_attempts'] != "None":
                pph['suicide_methods'] = st.multiselect(
                    "Methods Used in Attempts",
                    ["Overdose", "Cutting", "Hanging", "Jumping", "Firearms", "Other"],
                    default=pph.get('suicide_methods', [])
                )
        
        with col2:
            pph['self_harm_history'] = st.selectbox(
                "Self-harm History",
                ["", "None", "Past only", "Recent (within 1 year)", "Current"],
                index=0 if not pph.get('self_harm_history') else ["", "None", "Past only", "Recent (within 1 year)", "Current"].index(pph.get('self_harm_history'))
            )
            
            if pph.get('self_harm_history') and pph['self_harm_history'] != "None":
                pph['self_harm_methods'] = st.multiselect(
                    "Self-harm Methods",
                    ["Cutting", "Burning", "Hitting", "Hair pulling", "Scratching", "Other"],
                    default=pph.get('self_harm_methods', [])
                )
        
        st.session_state.patient_data['past_psychiatric_history'] = pph

    # Section 4: Past Medical History
    elif st.session_state.current_section == 4:
        st.markdown('<div class="section-header">Past Medical History</div>', unsafe_allow_html=True)
        
        if 'past_medical_history' not in st.session_state.patient_data:
            st.session_state.patient_data['past_medical_history'] = {}
        
        pmh = st.session_state.patient_data['past_medical_history']
        
        # Medical Conditions by System
        st.markdown("### 🏥 Medical Conditions by System")
        
        if 'medical_conditions' not in pmh:
            pmh['medical_conditions'] = {}
        
        # Cardiovascular
        with st.expander("❤️ Cardiovascular", expanded=False):
            with st.form("cardiovascular_form"):
                cardiovascular = st.multiselect(
                    "Cardiovascular Conditions",
                    ["Hypertension", "Coronary artery disease", "Heart failure", "Arrhythmias", 
                     "Valvular disease", "Peripheral vascular disease", "Deep vein thrombosis", 
                     "Pulmonary embolism", "Stroke", "TIA"],
                    default=pmh['medical_conditions'].get('cardiovascular', [])
                )
                
                cardio_submitted = st.form_submit_button("💾 Update Cardiovascular", use_container_width=True)
                
                if cardio_submitted:
                    pmh['medical_conditions']['cardiovascular'] = cardiovascular
                    st.success("✅ Cardiovascular conditions updated successfully!")
                    
                    if st.session_state.get('auto_save_enabled', True):
                        try:
                            save_assessment_data(st.session_state.patient_data)
                            st.info("💾 Auto-saved to database")
                        except Exception as e:
                            st.warning(f"Auto-save failed: {str(e)}")
        
        # Endocrine
        with st.expander("🦋 Endocrine", expanded=False):
            pmh['medical_conditions']['endocrine'] = st.multiselect(
                "Endocrine Conditions",
                ["Diabetes mellitus Type 1", "Diabetes mellitus Type 2", "Thyroid disorders", 
                 "Hyperthyroidism", "Hypothyroidism", "Adrenal disorders", "Pituitary disorders", 
                 "Metabolic syndrome", "Obesity"],
                default=pmh['medical_conditions'].get('endocrine', [])
            )
        
        # Neurological
        with st.expander("🧠 Neurological", expanded=False):
            pmh['medical_conditions']['neurological'] = st.multiselect(
                "Neurological Conditions",
                ["Seizure disorder", "Epilepsy", "Migraine", "Tension headaches", "Multiple sclerosis", 
                 "Parkinson's disease", "Dementia", "Traumatic brain injury", "Neuropathy", 
                 "Movement disorders"],
                default=pmh['medical_conditions'].get('neurological', [])
            )
        
        # Respiratory
        with st.expander("🫁 Respiratory", expanded=False):
            pmh['medical_conditions']['respiratory'] = st.multiselect(
                "Respiratory Conditions",
                ["Asthma", "COPD", "Sleep apnea", "Pneumonia", "Tuberculosis", 
                 "Pulmonary fibrosis", "Lung cancer", "Chronic bronchitis"],
                default=pmh['medical_conditions'].get('respiratory', [])
            )
        
        # Gastrointestinal
        with st.expander("🍽️ Gastrointestinal", expanded=False):
            pmh['medical_conditions']['gastrointestinal'] = st.multiselect(
                "Gastrointestinal Conditions",
                ["GERD", "Peptic ulcer disease", "IBD", "IBS", "Hepatitis", "Cirrhosis", 
                 "Gallbladder disease", "Pancreatitis", "Colon cancer"],
                default=pmh['medical_conditions'].get('gastrointestinal', [])
            )
        
        # Current Medications by Category
        st.markdown("### 💊 Current Medications")
        
        if 'medications' not in pmh:
            pmh['medications'] = {}
        
        # Psychiatric Medications
        with st.expander("🧠 Psychiatric Medications", expanded=True):
            col1, col2 = st.columns(2)
            
            with col1:
                pmh['medications']['antidepressants'] = st.multiselect(
                    "Antidepressants",
                    ["Sertraline (Zoloft)", "Fluoxetine (Prozac)", "Escitalopram (Lexapro)", 
                     "Paroxetine (Paxil)", "Citalopram (Celexa)", "Venlafaxine (Effexor)", 
                     "Duloxetine (Cymbalta)", "Bupropion (Wellbutrin)", "Mirtazapine (Remeron)", 
                     "Trazodone", "Amitriptyline", "Nortriptyline", "Other"],
                    default=pmh['medications'].get('antidepressants', [])
                )
                
                pmh['medications']['mood_stabilizers'] = st.multiselect(
                    "Mood Stabilizers",
                    ["Lithium", "Valproic acid (Depakote)", "Carbamazepine (Tegretol)", 
                     "Lamotrigine (Lamictal)", "Oxcarbazepine (Trileptal)", "Other"],
                    default=pmh['medications'].get('mood_stabilizers', [])
                )
            
            with col2:
                pmh['medications']['antipsychotics'] = st.multiselect(
                    "Antipsychotics",
                    ["Risperidone (Risperdal)", "Olanzapine (Zyprexa)", "Quetiapine (Seroquel)", 
                     "Aripiprazole (Abilify)", "Ziprasidone (Geodon)", "Paliperidone (Invega)", 
                     "Haloperidol (Haldol)", "Chlorpromazine (Thorazine)", "Clozapine (Clozaril)", "Other"],
                    default=pmh['medications'].get('antipsychotics', [])
                )
                
                pmh['medications']['anxiolytics'] = st.multiselect(
                    "Anxiolytics/Sedatives",
                    ["Lorazepam (Ativan)", "Alprazolam (Xanax)", "Clonazepam (Klonopin)", 
                     "Diazepam (Valium)", "Temazepam (Restoril)", "Zolpidem (Ambien)", 
                     "Eszopiclone (Lunesta)", "Hydroxyzine (Vistaril)", "Other"],
                    default=pmh['medications'].get('anxiolytics', [])
                )
        
        # Medical Medications
        with st.expander("🏥 Medical Medications", expanded=False):
            col1, col2 = st.columns(2)
            
            with col1:
                pmh['medications']['cardiovascular_meds'] = st.multiselect(
                    "Cardiovascular Medications",
                    ["ACE inhibitors", "ARBs", "Beta blockers", "Calcium channel blockers", 
                     "Diuretics", "Statins", "Anticoagulants", "Antiplatelets", "Other"],
                    default=pmh['medications'].get('cardiovascular_meds', [])
                )
                
                pmh['medications']['diabetes_meds'] = st.multiselect(
                    "Diabetes Medications",
                    ["Metformin", "Insulin", "Sulfonylureas", "DPP-4 inhibitors", 
                     "GLP-1 agonists", "SGLT-2 inhibitors", "Other"],
                    default=pmh['medications'].get('diabetes_meds', [])
                )
            
            with col2:
                pmh['medications']['pain_meds'] = st.multiselect(
                    "Pain Medications",
                    ["Acetaminophen", "NSAIDs", "Opioids", "Muscle relaxants", 
                     "Anticonvulsants for pain", "Topical analgesics", "Other"],
                    default=pmh['medications'].get('pain_meds', [])
                )
                
                pmh['medications']['other_meds'] = st.text_area(
                    "Other Medications",
                    value=pmh['medications'].get('other_meds', ''),
                    height=80,
                    help="List any other medications not covered above"
                )
        
        # Allergies and Adverse Reactions
        st.markdown("### ⚠️ Allergies and Adverse Reactions")
        col1, col2 = st.columns(2)
        
        with col1:
            if 'allergies' not in pmh:
                pmh['allergies'] = {}
            
            pmh['allergies']['drug_allergies'] = st.multiselect(
                "Drug Allergies",
                ["Penicillin", "Sulfa drugs", "NSAIDs", "Codeine", "Morphine", 
                 "Contrast dye", "Latex", "Other"],
                default=pmh['allergies'].get('drug_allergies', [])
            )
            
            pmh['allergies']['environmental_allergies'] = st.multiselect(
                "Environmental Allergies",
                ["Pollen", "Dust mites", "Pet dander", "Mold", "Food allergies", "Other"],
                default=pmh['allergies'].get('environmental_allergies', [])
            )
        
        with col2:
            pmh['allergies']['reaction_details'] = st.text_area(
                "Allergy Reaction Details",
                value=pmh['allergies'].get('reaction_details', ''),
                height=100,
                help="Describe specific reactions (rash, anaphylaxis, etc.)"
            )
        
        # Surgical History
        st.markdown("### 🔪 Surgical History")
        if 'surgeries' not in pmh:
            pmh['surgeries'] = {}
        
        pmh['surgeries']['surgical_procedures'] = st.multiselect(
            "Previous Surgeries",
            ["Appendectomy", "Cholecystectomy", "Hernia repair", "Joint replacement", 
             "Cardiac surgery", "Neurosurgery", "Gynecological surgery", "Urological surgery", 
             "Gastrointestinal surgery", "Other"],
            default=pmh['surgeries'].get('surgical_procedures', [])
        )
        
        if pmh['surgeries']['surgical_procedures']:
            pmh['surgeries']['surgery_details'] = st.text_area(
                "Surgery Details and Dates",
                value=pmh['surgeries'].get('surgery_details', ''),
                height=80,
                help="Provide dates and any complications"
            )
        
        st.session_state.patient_data['past_medical_history'] = pmh

    # Section 5: Family History
    elif st.session_state.current_section == 5:
        st.markdown('<div class="section-header">Family History</div>', unsafe_allow_html=True)
        
        if 'family_history' not in st.session_state.patient_data:
            st.session_state.patient_data['family_history'] = {}
        
        fh = st.session_state.patient_data['family_history']
        
        col1, col2 = st.columns(2)
        with col1:
            with st.form("family_psychiatric_form"):
                psychiatric_history = st.multiselect(
                    "Family Psychiatric History",
                    ["Depression", "Anxiety", "Bipolar Disorder", "Schizophrenia", "Substance Use", "Suicide", "Other", "None known"],
                    default=fh.get('psychiatric_history', [])
                )
                
                psychiatric_submitted = st.form_submit_button("💾 Update Psychiatric History", use_container_width=True)
                
                if psychiatric_submitted:
                    fh['psychiatric_history'] = psychiatric_history
                    st.success("✅ Family psychiatric history updated successfully!")
                    
                    if st.session_state.get('auto_save_enabled', True):
                        try:
                            save_assessment_data(st.session_state.patient_data)
                            st.info("💾 Auto-saved to database")
                        except Exception as e:
                            st.warning(f"Auto-save failed: {str(e)}")
            
            fh['medical_history'] = st.text_area(
                "Family Medical History",
                value=fh.get('medical_history', ''),
                height=100
            )
        
        with col2:
            fh['family_structure'] = st.text_area(
                "Family Structure",
                value=fh.get('family_structure', ''),
                height=100,
                help="Describe family composition and relationships"
            )
            
            fh['family_support'] = st.selectbox(
                "Family Support Level",
                ["Strong", "Moderate", "Limited", "None"],
                index=0 if not fh.get('family_support') else ["Strong", "Moderate", "Limited", "None"].index(fh.get('family_support'))
            )
        
        st.session_state.patient_data['family_history'] = fh

    # Section 6: Social & Developmental History
    elif st.session_state.current_section == 6:
        st.markdown('<div class="section-header">Social & Developmental History</div>', unsafe_allow_html=True)
        
        if 'social_developmental_history' not in st.session_state.patient_data:
            st.session_state.patient_data['social_developmental_history'] = {}
        
        sdh = st.session_state.patient_data['social_developmental_history']
        
        # Childhood and Development
        st.markdown("### 👶 Childhood and Early Development")
        col1, col2 = st.columns(2)
        
        with col1:
            if 'childhood' not in sdh:
                sdh['childhood'] = {}
            
            sdh['childhood']['birth_complications'] = st.multiselect(
                "Birth/Pregnancy Complications",
                ["None known", "Premature birth", "Low birth weight", "Cesarean delivery", 
                 "Complications during pregnancy", "Complications during delivery", "NICU stay", "Other"],
                default=sdh['childhood'].get('birth_complications', [])
            )
            
            sdh['childhood']['developmental_milestones'] = st.selectbox(
                "Developmental Milestones",
                ["", "Normal/on time", "Some delays", "Significant delays", "Unknown"],
                index=0 if not sdh['childhood'].get('developmental_milestones') else ["", "Normal/on time", "Some delays", "Significant delays", "Unknown"].index(sdh['childhood'].get('developmental_milestones'))
            )
            
            sdh['childhood']['childhood_illnesses'] = st.multiselect(
                "Significant Childhood Illnesses",
                ["None", "Frequent infections", "Asthma", "Seizures", "Head injury", 
                 "Chronic medical condition", "Hospitalizations", "Other"],
                default=sdh['childhood'].get('childhood_illnesses', [])
            )
        
        with col2:
            sdh['childhood']['school_performance'] = st.selectbox(
                "School Performance",
                ["", "Excellent", "Good", "Average", "Below average", "Poor", "Special education"],
                index=0 if not sdh['childhood'].get('school_performance') else ["", "Excellent", "Good", "Average", "Below average", "Poor", "Special education"].index(sdh['childhood'].get('school_performance'))
            )
            
            sdh['childhood']['behavioral_problems'] = st.multiselect(
                "Childhood Behavioral Problems",
                ["None", "ADHD symptoms", "Oppositional behavior", "Aggression", "Social difficulties", 
                 "Learning disabilities", "Anxiety", "Depression", "Autism spectrum symptoms", "Other"],
                default=sdh['childhood'].get('behavioral_problems', [])
            )
            
            sdh['childhood']['family_structure'] = st.selectbox(
                "Childhood Family Structure",
                ["", "Two-parent household", "Single parent", "Divorced parents", "Blended family", 
                 "Raised by grandparents", "Foster care", "Adoption", "Other relatives", "Other"],
                index=0 if not sdh['childhood'].get('family_structure') else ["", "Two-parent household", "Single parent", "Divorced parents", "Blended family", "Raised by grandparents", "Foster care", "Adoption", "Other relatives", "Other"].index(sdh['childhood'].get('family_structure'))
            )
        
        # Trauma and Adverse Experiences
        st.markdown("### ⚠️ Trauma and Adverse Childhood Experiences")
        if 'trauma' not in sdh:
            sdh['trauma'] = {}
        
        with st.form("childhood_trauma_form"):
            childhood_trauma = st.multiselect(
                "Childhood Trauma/Adverse Experiences",
                ["None", "Physical abuse", "Sexual abuse", "Emotional abuse", "Neglect", 
                 "Domestic violence witnessed", "Parent with mental illness", "Parent with substance abuse", 
                 "Incarcerated family member", "Death of parent/caregiver", "Serious family illness", 
                 "Poverty", "Homelessness", "Bullying", "Other"],
                default=sdh.get('trauma', {}).get('childhood_trauma', [])
            )
            
            adult_trauma = st.multiselect(
                "Adult Trauma Experiences",
                ["None", "Physical assault", "Sexual assault", "Domestic violence", "Combat exposure", 
                 "Motor vehicle accident", "Natural disaster", "Serious illness/injury", "Death of loved one", 
                 "Workplace violence", "Robbery/mugging", "Other violent crime", "Medical trauma", "Other"],
                default=sdh.get('trauma', {}).get('adult_trauma', [])
            )
            
            trauma_submitted = st.form_submit_button("💾 Update Trauma History", use_container_width=True)
            
            if trauma_submitted:
                sdh['trauma'].update({
                    'childhood_trauma': childhood_trauma,
                    'adult_trauma': adult_trauma
                })
                st.success("✅ Trauma history updated successfully!")
                
                if st.session_state.get('auto_save_enabled', True):
                    try:
                        save_assessment_data(st.session_state.patient_data)
                        st.info("💾 Auto-saved to database")
                    except Exception as e:
                        st.warning(f"Auto-save failed: {str(e)}")
        
        if sdh.get('trauma', {}).get('childhood_trauma', []) != ["None"] or sdh.get('trauma', {}).get('adult_trauma', []) != ["None"]:
            sdh['trauma']['trauma_treatment'] = st.multiselect(
                "Trauma Treatment Received",
                ["None", "Individual therapy", "Group therapy", "EMDR", "Cognitive processing therapy", 
                 "Prolonged exposure therapy", "Medication", "Support groups", "Other"],
                default=sdh['trauma'].get('trauma_treatment', [])
            )
        
        # Social Relationships
        st.markdown("### 👥 Social Relationships and Support")
        col1, col2 = st.columns(2)
        
        with col1:
            if 'relationships' not in sdh:
                sdh['relationships'] = {}
            
            sdh['relationships']['marital_status'] = st.selectbox(
                "Current Relationship Status",
                ["", "Single", "Dating", "Married", "Divorced", "Separated", "Widowed", "Cohabiting"],
                index=0 if not sdh['relationships'].get('marital_status') else ["", "Single", "Dating", "Married", "Divorced", "Separated", "Widowed", "Cohabiting"].index(sdh['relationships'].get('marital_status'))
            )
            
            sdh['relationships']['relationship_quality'] = st.selectbox(
                "Current Relationship Quality",
                ["", "Excellent", "Good", "Fair", "Poor", "Conflicted", "Not applicable"],
                index=0 if not sdh['relationships'].get('relationship_quality') else ["", "Excellent", "Good", "Fair", "Poor", "Conflicted", "Not applicable"].index(sdh['relationships'].get('relationship_quality'))
            )
            
            sdh['relationships']['social_support'] = st.selectbox(
                "Social Support Level",
                ["", "Strong support network", "Moderate support", "Limited support", "Isolated", "No support"],
                index=0 if not sdh['relationships'].get('social_support') else ["", "Strong support network", "Moderate support", "Limited support", "Isolated", "No support"].index(sdh['relationships'].get('social_support'))
            )
        
        with col2:
            sdh['relationships']['children'] = st.selectbox(
                "Children",
                ["", "None", "1 child", "2 children", "3 children", "4+ children"],
                index=0 if not sdh['relationships'].get('children') else ["", "None", "1 child", "2 children", "3 children", "4+ children"].index(sdh['relationships'].get('children'))
            )
            
            sdh['relationships']['relationship_problems'] = st.multiselect(
                "Relationship Problems",
                ["None", "Communication issues", "Trust issues", "Intimacy problems", "Financial disagreements", 
                 "Parenting disagreements", "In-law problems", "Jealousy", "Domestic violence", "Infidelity", "Other"],
                default=sdh['relationships'].get('relationship_problems', [])
            )
            
            sdh['relationships']['social_activities'] = st.multiselect(
                "Social Activities",
                ["None", "Family gatherings", "Friends", "Religious activities", "Community groups", 
                 "Sports/recreation", "Hobbies", "Volunteering", "Online communities", "Other"],
                default=sdh['relationships'].get('social_activities', [])
            )
        
        # Legal History
        st.markdown("### ⚖️ Legal History")
        if 'legal' not in sdh:
            sdh['legal'] = {}
        
        col1, col2 = st.columns(2)
        
        with col1:
            sdh['legal']['legal_problems'] = st.multiselect(
                "Legal Problems",
                ["None", "DUI/DWI", "Drug charges", "Theft", "Assault", "Domestic violence", 
                 "Traffic violations", "Probation violations", "Civil lawsuits", "Bankruptcy", "Other"],
                default=sdh['legal'].get('legal_problems', [])
            )
            
            sdh['legal']['incarceration_history'] = st.selectbox(
                "Incarceration History",
                ["", "None", "Jail (days)", "Jail (weeks)", "Prison (months)", "Prison (years)", "Multiple times"],
                index=0 if not sdh['legal'].get('incarceration_history') else ["", "None", "Jail (days)", "Jail (weeks)", "Prison (months)", "Prison (years)", "Multiple times"].index(sdh['legal'].get('incarceration_history'))
            )
        
        with col2:
            sdh['legal']['current_legal_status'] = st.selectbox(
                "Current Legal Status",
                ["", "No legal issues", "Pending charges", "On probation", "On parole", "Court-ordered treatment", "Other"],
                index=0 if not sdh['legal'].get('current_legal_status') else ["", "No legal issues", "Pending charges", "On probation", "On parole", "Court-ordered treatment", "Other"].index(sdh['legal'].get('current_legal_status'))
            )
            
            sdh['legal']['legal_consequences'] = st.multiselect(
                "Legal Consequences of Mental Health/Substance Use",
                ["None", "Arrests", "Fines", "License suspension", "Job loss", "Custody issues", 
                 "Restraining orders", "Mandated treatment", "Other"],
                default=sdh['legal'].get('legal_consequences', [])
            )
        
        # Cultural and Religious Background
        st.markdown("### 🌍 Cultural and Religious Background")
        if 'cultural' not in sdh:
            sdh['cultural'] = {}
        
        col1, col2 = st.columns(2)
        
        with col1:
            sdh['cultural']['cultural_background'] = st.text_input(
                "Cultural/Ethnic Background",
                value=sdh['cultural'].get('cultural_background', ''),
                help="Patient's cultural or ethnic identity"
            )
            
            sdh['cultural']['primary_language'] = st.selectbox(
                "Primary Language",
                ["", "English", "Spanish", "Mandarin", "Arabic", "French", "German", "Other"],
                index=0 if not sdh['cultural'].get('primary_language') else ["", "English", "Spanish", "Mandarin", "Arabic", "French", "German", "Other"].index(sdh['cultural'].get('primary_language'))
            )
        
        with col2:
            sdh['cultural']['religious_spiritual'] = st.selectbox(
                "Religious/Spiritual Beliefs",
                ["", "Christian", "Catholic", "Jewish", "Muslim", "Hindu", "Buddhist", "Atheist", "Agnostic", "Spiritual but not religious", "Other"],
                index=0 if not sdh['cultural'].get('religious_spiritual') else ["", "Christian", "Catholic", "Jewish", "Muslim", "Hindu", "Buddhist", "Atheist", "Agnostic", "Spiritual but not religious", "Other"].index(sdh['cultural'].get('religious_spiritual'))
            )
            
            sdh['cultural']['cultural_factors'] = st.multiselect(
                "Cultural Factors Affecting Treatment",
                ["None", "Language barriers", "Religious beliefs", "Cultural stigma", "Family expectations", 
                 "Traditional healing practices", "Gender roles", "Immigration status", "Other"],
                default=sdh['cultural'].get('cultural_factors', [])
            )
        
        st.session_state.patient_data['social_developmental_history'] = sdh

    # Section 7: Enhanced DSM-5 Substance Use Assessment
    elif st.session_state.current_section == 7:
        # Render enhanced CSS
        render_enhanced_css()
        
        st.markdown('<div class="section-header">DSM-5 Comprehensive Substance Use Assessment</div>', unsafe_allow_html=True)
        
        # Add section progress indicator
        render_section_progress(completed_sections=6, current_section=7, total_sections=16)
        
        # Validation errors display with recovery options
        if 'substance_use_errors' in st.session_state.validation_errors and st.session_state.validation_errors['substance_use_errors']:
            st.error("⚠️ **Substance Use Data Validation Issues Detected:**")
            for error in st.session_state.validation_errors['substance_use_errors']:
                st.write(f"• {error}")
            
            col1, col2, col3 = st.columns(3)
            with col1:
                if st.button("🔧 Recover Substance Data", help="Attempt to recover from last known good state"):
                    try:
                        recovered = recover_corrupted_data("substance")
                        if 'substance_use' in recovered:
                            st.session_state.patient_data['substance_use'] = recovered['substance_use']
                            st.success("✅ Substance use data recovered successfully!")
                            st.rerun()
                        else:
                            st.warning("⚠️ No recoverable data found, initialized with clean structure")
                    except Exception as e:
                        st.error(f"❌ Recovery failed: {e}")
            
            with col2:
                if st.button("🗑️ Reset Substance Data", help="Clear all substance use data and start fresh"):
                    st.session_state.patient_data['substance_use'] = {
                        category: {'used': False} for category in DSM5_SUBSTANCE_CATEGORIES.keys()
                    }
                    st.success("✅ Substance use data reset successfully!")
                    st.rerun()
            
            with col3:
                if st.button("🔄 Revalidate Data", help="Run validation again on current data"):
                    validation_results = validate_comprehensive_assessment(st.session_state.patient_data)
                    st.session_state.validation_errors = validation_results
                    if not validation_results.get('substance_use_errors'):
                        st.success("✅ Validation passed!")
                    st.rerun()
        
        if 'substance_use' not in st.session_state.patient_data:
            st.session_state.patient_data['substance_use'] = {}
        
        substance_data = st.session_state.patient_data['substance_use']
        
        # Migrate existing data if needed
        if 'drugs' in substance_data and 'substances_used' in substance_data['drugs']:
            substance_data = migrate_existing_substance_data(substance_data)
        
        # Quick Screening with Enhanced UI
        st.markdown("### 🔍 Quick Screening")
        st.markdown("""
        <div class="clinical-guidance">
            <h4>📋 Screening Overview</h4>
            <p>Complete this brief screening to guide the detailed assessment. Positive responses will expand relevant sections automatically.</p>
        </div>
        """, unsafe_allow_html=True)
        
        col1, col2, col3 = st.columns(3)
        with col1:
            substance_data['any_substance_use'] = st.selectbox(
                "Any Substance Use (including alcohol)",
                ["No", "Yes - past only", "Yes - current"],
                index=0 if not substance_data.get('any_substance_use') else ["No", "Yes - past only", "Yes - current"].index(substance_data.get('any_substance_use')),
                help="Include all substances: alcohol, drugs, tobacco, caffeine"
            )
        
        with col2:
            substance_data['substance_related_problems'] = st.selectbox(
                "Substance-Related Problems",
                ["None", "Mild", "Moderate", "Severe"],
                index=0 if not substance_data.get('substance_related_problems') else ["None", "Mild", "Moderate", "Severe"].index(substance_data.get('substance_related_problems')),
                help="Consider legal, social, occupational, or health problems"
            )
        
        with col3:
            substance_data['dsm5_criteria_met'] = st.selectbox(
                "DSM-5 Criteria Assessment",
                ["Not assessed", "No disorder", "Mild", "Moderate", "Severe"],
                index=0 if not substance_data.get('dsm5_criteria_met') else ["Not assessed", "No disorder", "Mild", "Moderate", "Severe"].index(substance_data.get('dsm5_criteria_met')),
                help="Mild: 2-3 criteria, Moderate: 4-5 criteria, Severe: 6+ criteria"
            )
        
        # Determine if detailed assessment should be expanded
        expand_detailed = substance_data.get('any_substance_use') != "No"
        
        # DSM-5 Substance Categories Assessment with Progressive Disclosure
        st.markdown("### 📋 DSM-5 Substance Categories")
        
        # Show enhanced summary and navigation aids if any substances are used
        if expand_detailed and 'substances' in substance_data:
            render_substance_use_summary(substance_data)
            render_smart_navigation_aids(substance_data, "substance")
        
        st.markdown("""
        <div class="clinical-guidance">
            <h4>🎯 Assessment Strategy</h4>
            <p>Focus on substances the patient has used. Each category includes specific DSM-5 criteria and clinical considerations. 
            Sections will expand automatically based on screening responses.</p>
        </div>
        """, unsafe_allow_html=True)
        
        # Initialize substance categories data
        if 'substances' not in substance_data:
            substance_data['substances'] = {}
        
        # Alcohol Assessment (DSM-5 Category 1) with Enhanced UI
        create_smart_expander(
            "🍷 Alcohol Use Disorder Assessment", 
            lambda: render_alcohol_assessment(substance_data),
            expanded=expand_detailed,
            help_text="Most common substance use disorder. Focus on tolerance, withdrawal, and functional impairment.",
            category_color="alcohol"
        )
        
        if 'Alcohol' not in substance_data['substances']:
            substance_data['substances']['Alcohol'] = {'used': False}
        
        alcohol_data = substance_data['substances']['Alcohol']
        
        # Basic alcohol use assessment
        st.markdown("#### 🔍 Basic Assessment")
        col1, col2, col3 = st.columns(3)
        with col1:
            alcohol_data['used'] = st.checkbox(
                "Alcohol Use",
                value=alcohol_data.get('used', False),
                help="Check if patient has ever used alcohol",
                key="alcohol_use_detailed"
            )
            
            if alcohol_data['used']:
                alcohol_data['current_use'] = st.selectbox(
                    "Current Alcohol Use",
                    ["Never used", "Abstinent (>1 year)", "Abstinent (<1 year)", "Occasional social", "Regular use", "Heavy use"],
                    index=0 if not alcohol_data.get('current_use') else ["Never used", "Abstinent (>1 year)", "Abstinent (<1 year)", "Occasional social", "Regular use", "Heavy use"].index(alcohol_data.get('current_use')),
                    help="Select current pattern of alcohol use"
                )
                
                alcohol_data['age_onset'] = st.number_input(
                    "Age First Used Alcohol",
                    min_value=5, max_value=80,
                    value=alcohol_data.get('age_onset') if alcohol_data.get('age_onset') is not None else 18,
                    help="Age when patient first tried alcohol"
                )
        
        with col2:
            if alcohol_data.get('used'):
                alcohol_data['substances'] = st.multiselect(
                    "Types of Alcohol Used",
                    DSM5_SUBSTANCE_CATEGORIES['Alcohol']['substances'],
                    default=alcohol_data.get('substances', [])
                )
                
                if alcohol_data['current_use'] not in ["Never used", "Abstinent (>1 year)", "Abstinent (<1 year)"]:
                    alcohol_data['drinks_per_week'] = st.number_input(
                        "Drinks per Week",
                        min_value=0, max_value=100,
                        value=alcohol_data.get('drinks_per_week', 0)
                    )
                    
                    alcohol_data['binge_episodes'] = st.selectbox(
                        "Binge Drinking (5+ drinks)",
                        ["Never", "Monthly", "Weekly", "Multiple times/week", "Daily"],
                        index=0 if not alcohol_data.get('binge_episodes') else ["Never", "Monthly", "Weekly", "Multiple times/week", "Daily"].index(alcohol_data.get('binge_episodes'))
                    )
        
        with col3:
            if alcohol_data.get('used'):
                alcohol_data['last_drink'] = st.selectbox(
                    "Last Drink",
                    ["Never", "Today", "Yesterday", "This week", "This month", "Months ago", "Years ago"],
                    index=0 if not alcohol_data.get('last_drink') else ["Never", "Today", "Yesterday", "This week", "This month", "Months ago", "Years ago"].index(alcohol_data.get('last_drink'))
                )
                
                alcohol_data['treatment_history'] = st.selectbox(
                    "Alcohol Treatment",
                    ["None", "AA/NA meetings", "Outpatient", "Inpatient", "Multiple treatments"],
                    index=0 if not alcohol_data.get('treatment_history') else ["None", "AA/NA meetings", "Outpatient", "Inpatient", "Multiple treatments"].index(alcohol_data.get('treatment_history'))
                )
            
            # DSM-5 Criteria for Alcohol with Enhanced Presentation
            if alcohol_data.get('used'):
                st.markdown("#### 📋 DSM-5 Alcohol Use Disorder Criteria")
                st.markdown("""
                <div class="dsm5-criteria">
                    <p><strong>Instructions:</strong> Select all criteria present in the past 12 months. 
                    Minimum 2 criteria required for diagnosis.</p>
                </div>
                """, unsafe_allow_html=True)
                
                alcohol_data['dsm5_criteria'] = st.multiselect(
                    "Select all criteria that apply:",
                    DSM5_SUBSTANCE_CATEGORIES['Alcohol']['dsm5_criteria'],
                    default=alcohol_data.get('dsm5_criteria', []),
                    help="DSM-5 criteria for Alcohol Use Disorder - select all that apply in past 12 months"
                )
                
                # Calculate and display severity with enhanced visual indicator
                criteria_count = len(alcohol_data.get('dsm5_criteria', []))
                alcohol_data['severity'] = get_dsm5_severity(criteria_count)
                
                st.markdown("#### 🎯 Severity Assessment")
                st.markdown(render_severity_indicator(alcohol_data['severity'], criteria_count), unsafe_allow_html=True)
                
                # Consequences with better organization
                st.markdown("#### ⚠️ Alcohol-Related Consequences")
                alcohol_data['consequences'] = st.multiselect(
                    "Select all consequences experienced:",
                    ["Legal issues (DUI, arrests)", "Work/school problems", "Relationship problems", "Financial problems", 
                     "Health problems", "Blackouts", "Memory problems", "Accidents/injuries", "Failed quit attempts"],
                    default=alcohol_data.get('consequences', []),
                    help="Document all negative consequences related to alcohol use"
                )
            
            st.markdown('</div>', unsafe_allow_html=True)
        
        # Cannabis Assessment (DSM-5 Category 2) with Enhanced UI
        with st.expander("🌿 Cannabis Use Disorder Assessment", expanded=False):
            st.markdown('<div class="substance-category cannabis">', unsafe_allow_html=True)
            render_dsm5_criteria_guidance("Cannabis")
            if 'Cannabis' not in substance_data['substances']:
                substance_data['substances']['Cannabis'] = {'used': False}
            
            cannabis_data = substance_data['substances']['Cannabis']
            
            col1, col2 = st.columns(2)
            with col1:
                cannabis_data['used'] = st.checkbox("Cannabis Use", value=cannabis_data.get('used', False))
                
                if cannabis_data['used']:
                    cannabis_data['substances'] = st.multiselect(
                        "Types of Cannabis Used",
                        DSM5_SUBSTANCE_CATEGORIES['Cannabis']['substances'],
                        default=cannabis_data.get('substances', [])
                    )
                    
                    cannabis_data['frequency'] = st.selectbox(
                        "Frequency of Use",
                        ["Daily", "Several times/week", "Weekly", "Monthly", "Occasionally", "Past use only"],
                        index=0 if not cannabis_data.get('frequency') else ["Daily", "Several times/week", "Weekly", "Monthly", "Occasionally", "Past use only"].index(cannabis_data.get('frequency'))
                    )
                    
                    cannabis_data['age_onset'] = st.number_input(
                        "Age First Used Cannabis",
                        min_value=5, max_value=80,
                        value=cannabis_data.get('age_onset') if cannabis_data.get('age_onset') is not None else 16
                    )
            
            with col2:
                if cannabis_data.get('used'):
                    cannabis_data['method_use'] = st.multiselect(
                        "Method of Use",
                        ["Smoking", "Vaping", "Edibles", "Concentrates/Dabs", "Other"],
                        default=cannabis_data.get('method_use', [])
                    )
                    
                    cannabis_data['potency'] = st.selectbox(
                        "Typical Potency",
                        ["Low THC", "Medium THC", "High THC", "Unknown", "CBD dominant"],
                        index=0 if not cannabis_data.get('potency') else ["Low THC", "Medium THC", "High THC", "Unknown", "CBD dominant"].index(cannabis_data.get('potency'))
                    )
            
            # DSM-5 Criteria for Cannabis with Enhanced Presentation
            if cannabis_data.get('used'):
                st.markdown("#### 📋 DSM-5 Cannabis Use Disorder Criteria")
                st.markdown("""
                <div class="dsm5-criteria">
                    <p><strong>Note:</strong> Cannabis withdrawal was added in DSM-5. Look for irritability, anxiety, 
                    and physical discomfort when use stops.</p>
                </div>
                """, unsafe_allow_html=True)
                
                cannabis_data['dsm5_criteria'] = st.multiselect(
                    "Select all criteria that apply:",
                    DSM5_SUBSTANCE_CATEGORIES['Cannabis']['dsm5_criteria'],
                    default=cannabis_data.get('dsm5_criteria', []),
                    help="DSM-5 criteria for Cannabis Use Disorder - select all that apply in past 12 months"
                )
                
                criteria_count = len(cannabis_data.get('dsm5_criteria', []))
                cannabis_data['severity'] = get_dsm5_severity(criteria_count)
                
                st.markdown("#### 🎯 Severity Assessment")
                st.markdown(render_severity_indicator(cannabis_data['severity'], criteria_count), unsafe_allow_html=True)
            
            st.markdown('</div>', unsafe_allow_html=True)
        
        # Stimulants Assessment (DSM-5 Category 3)
        with st.expander("⚡ Stimulant Use Disorder Assessment"):
            if 'Stimulants' not in substance_data['substances']:
                substance_data['substances']['Stimulants'] = {'used': False}
            
            stimulants_data = substance_data['substances']['Stimulants']
            
            col1, col2 = st.columns(2)
            with col1:
                stimulants_data['used'] = st.checkbox("Stimulant Use", value=stimulants_data.get('used', False))
                
                if stimulants_data['used']:
                    stimulants_data['substances'] = st.multiselect(
                        "Types of Stimulants Used",
                        DSM5_SUBSTANCE_CATEGORIES['Stimulants']['substances'],
                        default=stimulants_data.get('substances', [])
                    )
                    
                    stimulants_data['route'] = st.multiselect(
                        "Route of Administration",
                        ["Oral", "Nasal (snorting)", "Smoking", "Injection", "Other"],
                        default=stimulants_data.get('route', [])
                    )
            
            with col2:
                if stimulants_data.get('used'):
                    stimulants_data['frequency'] = st.selectbox(
                        "Frequency of Use",
                        ["Daily", "Several times/week", "Weekly", "Monthly", "Occasionally", "Past use only"],
                        index=0 if not stimulants_data.get('frequency') else ["Daily", "Several times/week", "Weekly", "Monthly", "Occasionally", "Past use only"].index(stimulants_data.get('frequency'))
                    )
                    
                    stimulants_data['psychotic_symptoms'] = st.checkbox(
                        "Stimulant-induced psychotic symptoms",
                        value=stimulants_data.get('psychotic_symptoms', False)
                    )
            
            # DSM-5 Criteria for Stimulants
            if stimulants_data.get('used'):
                st.markdown("#### DSM-5 Stimulant Use Disorder Criteria")
                stimulants_data['dsm5_criteria'] = st.multiselect(
                    "Select all criteria that apply:",
                    DSM5_SUBSTANCE_CATEGORIES['Stimulants']['dsm5_criteria'],
                    default=stimulants_data.get('dsm5_criteria', [])
                )
                
                criteria_count = len(stimulants_data.get('dsm5_criteria', []))
                stimulants_data['severity'] = get_dsm5_severity(criteria_count)
                st.info(f"**Severity Assessment:** {stimulants_data['severity']} ({criteria_count} criteria)")
        
        # Opioids Assessment (DSM-5 Category 4)
        with st.expander("💊 Opioid Use Disorder Assessment"):
            if 'Opioids' not in substance_data['substances']:
                substance_data['substances']['Opioids'] = {'used': False}
            
            opioids_data = substance_data['substances']['Opioids']
            
            col1, col2 = st.columns(2)
            with col1:
                opioids_data['used'] = st.checkbox("Opioid Use", value=opioids_data.get('used', False))
                
                if opioids_data['used']:
                    opioids_data['substances'] = st.multiselect(
                        "Types of Opioids Used",
                        DSM5_SUBSTANCE_CATEGORIES['Opioids']['substances'],
                        default=opioids_data.get('substances', [])
                    )
                    
                    opioids_data['route'] = st.multiselect(
                        "Route of Administration",
                        ["Oral", "Nasal (snorting)", "Smoking", "Injection", "Other"],
                        default=opioids_data.get('route', [])
                    )
            
            with col2:
                if opioids_data.get('used'):
                    opioids_data['overdose_history'] = st.selectbox(
                        "Overdose History",
                        ["Never", "1 time", "2-3 times", "4+ times"],
                        index=0 if not opioids_data.get('overdose_history') else ["Never", "1 time", "2-3 times", "4+ times"].index(opioids_data.get('overdose_history'))
                    )
                    
                    opioids_data['naloxone_use'] = st.checkbox(
                        "Naloxone (Narcan) ever used",
                        value=opioids_data.get('naloxone_use', False)
                    )
            
            # DSM-5 Criteria for Opioids
            if opioids_data.get('used'):
                st.markdown("#### DSM-5 Opioid Use Disorder Criteria")
                opioids_data['dsm5_criteria'] = st.multiselect(
                    "Select all criteria that apply:",
                    DSM5_SUBSTANCE_CATEGORIES['Opioids']['dsm5_criteria'],
                    default=opioids_data.get('dsm5_criteria', [])
                )
                
                criteria_count = len(opioids_data.get('dsm5_criteria', []))
                opioids_data['severity'] = get_dsm5_severity(criteria_count)
                st.info(f"**Severity Assessment:** {opioids_data['severity']} ({criteria_count} criteria)")
        
        # Sedatives/Hypnotics/Anxiolytics Assessment (DSM-5 Category 5)
        with st.expander("😴 Sedative/Hypnotic/Anxiolytic Use Disorder Assessment"):
            if 'Sedatives_Hypnotics_Anxiolytics' not in substance_data['substances']:
                substance_data['substances']['Sedatives_Hypnotics_Anxiolytics'] = {'used': False}
            
            sedatives_data = substance_data['substances']['Sedatives_Hypnotics_Anxiolytics']
            
            col1, col2 = st.columns(2)
            with col1:
                sedatives_data['used'] = st.checkbox("Sedative/Hypnotic/Anxiolytic Use", value=sedatives_data.get('used', False))
                
                if sedatives_data['used']:
                    sedatives_data['substances'] = st.multiselect(
                        "Types Used",
                        DSM5_SUBSTANCE_CATEGORIES['Sedatives_Hypnotics_Anxiolytics']['substances'],
                        default=sedatives_data.get('substances', [])
                    )
            
            with col2:
                if sedatives_data.get('used'):
                    sedatives_data['prescribed_vs_illicit'] = st.selectbox(
                        "Source",
                        ["Prescribed", "Illicit/Street", "Both prescribed and illicit"],
                        index=0 if not sedatives_data.get('prescribed_vs_illicit') else ["Prescribed", "Illicit/Street", "Both prescribed and illicit"].index(sedatives_data.get('prescribed_vs_illicit'))
                    )
            
            # DSM-5 Criteria for Sedatives/Hypnotics/Anxiolytics
            if sedatives_data.get('used'):
                st.markdown("#### DSM-5 Sedative/Hypnotic/Anxiolytic Use Disorder Criteria")
                sedatives_data['dsm5_criteria'] = st.multiselect(
                    "Select all criteria that apply:",
                    DSM5_SUBSTANCE_CATEGORIES['Sedatives_Hypnotics_Anxiolytics']['dsm5_criteria'],
                    default=sedatives_data.get('dsm5_criteria', [])
                )
                
                criteria_count = len(sedatives_data.get('dsm5_criteria', []))
                sedatives_data['severity'] = get_dsm5_severity(criteria_count)
                st.info(f"**Severity Assessment:** {sedatives_data['severity']} ({criteria_count} criteria)")
        
        # Hallucinogens Assessment (DSM-5 Category 6)
        with st.expander("🌈 Hallucinogen Use Disorder Assessment"):
            if 'Hallucinogens' not in substance_data['substances']:
                substance_data['substances']['Hallucinogens'] = {'used': False}
            
            hallucinogens_data = substance_data['substances']['Hallucinogens']
            
            col1, col2 = st.columns(2)
            with col1:
                hallucinogens_data['used'] = st.checkbox("Hallucinogen Use", value=hallucinogens_data.get('used', False))
                
                if hallucinogens_data['used']:
                    hallucinogens_data['substances'] = st.multiselect(
                        "Types of Hallucinogens Used",
                        DSM5_SUBSTANCE_CATEGORIES['Hallucinogens']['substances'],
                        default=hallucinogens_data.get('substances', [])
                    )
                    
                    hallucinogens_data['frequency'] = st.selectbox(
                        "Frequency of Use",
                        ["Daily", "Several times/week", "Weekly", "Monthly", "Occasionally", "Past use only"],
                        index=0 if not hallucinogens_data.get('frequency') else ["Daily", "Several times/week", "Weekly", "Monthly", "Occasionally", "Past use only"].index(hallucinogens_data.get('frequency'))
                    )
            
            with col2:
                if hallucinogens_data.get('used'):
                    hallucinogens_data['bad_trips'] = st.checkbox(
                        "History of bad trips",
                        value=hallucinogens_data.get('bad_trips', False)
                    )
                    
                    hallucinogens_data['flashbacks'] = st.checkbox(
                        "Flashbacks or persistent perceptual disturbances",
                        value=hallucinogens_data.get('flashbacks', False)
                    )
            
            # DSM-5 Criteria for Hallucinogens
            if hallucinogens_data.get('used'):
                st.markdown("#### DSM-5 Hallucinogen Use Disorder Criteria")
                hallucinogens_data['dsm5_criteria'] = st.multiselect(
                    "Select all criteria that apply:",
                    DSM5_SUBSTANCE_CATEGORIES['Hallucinogens']['dsm5_criteria'],
                    default=hallucinogens_data.get('dsm5_criteria', [])
                )
                
                criteria_count = len(hallucinogens_data.get('dsm5_criteria', []))
                hallucinogens_data['severity'] = get_dsm5_severity(criteria_count)
                st.info(f"**Severity Assessment:** {hallucinogens_data['severity']} ({criteria_count} criteria)")
        
        # Inhalants Assessment (DSM-5 Category 7)
        with st.expander("💨 Inhalant Use Disorder Assessment"):
            if 'Inhalants' not in substance_data['substances']:
                substance_data['substances']['Inhalants'] = {'used': False}
            
            inhalants_data = substance_data['substances']['Inhalants']
            
            col1, col2 = st.columns(2)
            with col1:
                inhalants_data['used'] = st.checkbox("Inhalant Use", value=inhalants_data.get('used', False))
                
                if inhalants_data['used']:
                    inhalants_data['substances'] = st.multiselect(
                        "Types of Inhalants Used",
                        DSM5_SUBSTANCE_CATEGORIES['Inhalants']['substances'],
                        default=inhalants_data.get('substances', [])
                    )
                    
                    inhalants_data['method'] = st.multiselect(
                        "Method of Use",
                        ["Sniffing", "Huffing", "Bagging", "Other"],
                        default=inhalants_data.get('method', [])
                    )
            
            with col2:
                if inhalants_data.get('used'):
                    inhalants_data['medical_complications'] = st.multiselect(
                        "Medical Complications",
                        ["None", "Respiratory problems", "Cardiac problems", "Neurological problems", "Liver damage", "Kidney damage", "Other"],
                        default=inhalants_data.get('medical_complications', [])
                    )
            
            # DSM-5 Criteria for Inhalants
            if inhalants_data.get('used'):
                st.markdown("#### DSM-5 Inhalant Use Disorder Criteria")
                inhalants_data['dsm5_criteria'] = st.multiselect(
                    "Select all criteria that apply:",
                    DSM5_SUBSTANCE_CATEGORIES['Inhalants']['dsm5_criteria'],
                    default=inhalants_data.get('dsm5_criteria', [])
                )
                
                criteria_count = len(inhalants_data.get('dsm5_criteria', []))
                inhalants_data['severity'] = get_dsm5_severity(criteria_count)
                st.info(f"**Severity Assessment:** {inhalants_data['severity']} ({criteria_count} criteria)")
        
        # Tobacco Assessment (DSM-5 Category 8)
        with st.expander("🚬 Tobacco Use Disorder Assessment"):
            if 'Tobacco' not in substance_data['substances']:
                substance_data['substances']['Tobacco'] = {'used': False}
            
            tobacco_data = substance_data['substances']['Tobacco']
            
            col1, col2, col3 = st.columns(3)
            with col1:
                tobacco_data['used'] = st.checkbox("Tobacco Use", value=tobacco_data.get('used', False))
                
                if tobacco_data['used']:
                    tobacco_data['current_use'] = st.selectbox(
                        "Current Tobacco Use",
                        ["Never used", "Former user", "Current user"],
                        index=0 if not tobacco_data.get('current_use') else ["Never used", "Former user", "Current user"].index(tobacco_data.get('current_use'))
                    )
                    
                    tobacco_data['substances'] = st.multiselect(
                        "Types of Tobacco Products",
                        DSM5_SUBSTANCE_CATEGORIES['Tobacco']['substances'],
                        default=tobacco_data.get('substances', [])
                    )
            
            with col2:
                if tobacco_data.get('used') and tobacco_data['current_use'] == "Current user":
                    tobacco_data['cigarettes_per_day'] = st.number_input(
                        "Cigarettes per Day",
                        min_value=0, max_value=60,
                        value=tobacco_data.get('cigarettes_per_day', 0)
                    )
                    
                    tobacco_data['time_to_first_cigarette'] = st.selectbox(
                        "Time to First Cigarette",
                        [">60 minutes", "31-60 minutes", "6-30 minutes", "≤5 minutes"],
                        index=0 if not tobacco_data.get('time_to_first_cigarette') else [">60 minutes", "31-60 minutes", "6-30 minutes", "≤5 minutes"].index(tobacco_data.get('time_to_first_cigarette'))
                    )
            
            with col3:
                if tobacco_data.get('used'):
                    tobacco_data['quit_attempts'] = st.selectbox(
                        "Quit Attempts",
                        ["None", "1-2 attempts", "3-5 attempts", "Many attempts"],
                        index=0 if not tobacco_data.get('quit_attempts') else ["None", "1-2 attempts", "3-5 attempts", "Many attempts"].index(tobacco_data.get('quit_attempts'))
                    )
                    
                    tobacco_data['age_onset'] = st.number_input(
                        "Age Started Regular Use",
                        min_value=5, max_value=80,
                        value=tobacco_data.get('age_onset') if tobacco_data.get('age_onset') is not None else 18
                    )
            
            # DSM-5 Criteria for Tobacco
            if tobacco_data.get('used'):
                st.markdown("#### DSM-5 Tobacco Use Disorder Criteria")
                tobacco_data['dsm5_criteria'] = st.multiselect(
                    "Select all criteria that apply:",
                    DSM5_SUBSTANCE_CATEGORIES['Tobacco']['dsm5_criteria'],
                    default=tobacco_data.get('dsm5_criteria', [])
                )
                
                criteria_count = len(tobacco_data.get('dsm5_criteria', []))
                tobacco_data['severity'] = get_dsm5_severity(criteria_count)
                st.info(f"**Severity Assessment:** {tobacco_data['severity']} ({criteria_count} criteria)")
        
        # Caffeine Assessment (DSM-5 Category 9)
        with st.expander("☕ Caffeine Use Disorder Assessment"):
            if 'Caffeine' not in substance_data['substances']:
                substance_data['substances']['Caffeine'] = {'used': False}
            
            caffeine_data = substance_data['substances']['Caffeine']
            
            col1, col2 = st.columns(2)
            with col1:
                caffeine_data['used'] = st.checkbox("Caffeine Use", value=caffeine_data.get('used', False))
                
                if caffeine_data['used']:
                    caffeine_data['substances'] = st.multiselect(
                        "Types of Caffeine Sources",
                        DSM5_SUBSTANCE_CATEGORIES['Caffeine']['substances'],
                        default=caffeine_data.get('substances', [])
                    )
                    
                    caffeine_data['daily_intake'] = st.selectbox(
                        "Daily Caffeine Intake",
                        ["None", "1-2 cups coffee/day", "3-5 cups coffee/day", "6+ cups coffee/day", "Energy drinks", "Excessive (>400mg/day)"],
                        index=0 if not caffeine_data.get('daily_intake') else ["None", "1-2 cups coffee/day", "3-5 cups coffee/day", "6+ cups coffee/day", "Energy drinks", "Excessive (>400mg/day)"].index(caffeine_data.get('daily_intake'))
                    )
            
            with col2:
                if caffeine_data.get('used'):
                    caffeine_data['withdrawal_symptoms'] = st.multiselect(
                        "Caffeine Withdrawal Symptoms",
                        ["None", "Headaches", "Fatigue", "Irritability", "Difficulty concentrating", "Depressed mood", "Flu-like symptoms"],
                        default=caffeine_data.get('withdrawal_symptoms', [])
                    )
                    
                    caffeine_data['sleep_impact'] = st.checkbox(
                        "Caffeine interferes with sleep",
                        value=caffeine_data.get('sleep_impact', False)
                    )
            
            # DSM-5 Criteria for Caffeine
            if caffeine_data.get('used'):
                st.markdown("#### DSM-5 Caffeine Use Disorder Criteria")
                caffeine_data['dsm5_criteria'] = st.multiselect(
                    "Select all criteria that apply:",
                    DSM5_SUBSTANCE_CATEGORIES['Caffeine']['dsm5_criteria'],
                    default=caffeine_data.get('dsm5_criteria', [])
                )
                
                criteria_count = len(caffeine_data.get('dsm5_criteria', []))
                caffeine_data['severity'] = get_dsm5_severity(criteria_count)
                st.info(f"**Severity Assessment:** {caffeine_data['severity']} ({criteria_count} criteria)")
        
        # Other/Unknown Substances Assessment (DSM-5 Category 10)
        with st.expander("❓ Other/Unknown Substance Use Disorder Assessment"):
            if 'Other_Unknown' not in substance_data['substances']:
                substance_data['substances']['Other_Unknown'] = {'used': False}
            
            other_data = substance_data['substances']['Other_Unknown']
            
            col1, col2 = st.columns(2)
            with col1:
                other_data['used'] = st.checkbox("Other/Unknown Substance Use", value=other_data.get('used', False))
                
                if other_data['used']:
                    other_data['substances'] = st.multiselect(
                        "Types of Other Substances",
                        DSM5_SUBSTANCE_CATEGORIES['Other_Unknown']['substances'],
                        default=other_data.get('substances', [])
                    )
                    
                    other_data['substance_details'] = st.text_area(
                        "Describe Other Substances",
                        value=other_data.get('substance_details', ''),
                        height=80,
                        help="Provide details about unknown or unlisted substances"
                    )
            
            with col2:
                if other_data.get('used'):
                    other_data['effects'] = st.multiselect(
                        "Effects Experienced",
                        ["Euphoria", "Sedation", "Stimulation", "Hallucinations", "Dissociation", "Other"],
                        default=other_data.get('effects', [])
                    )
                    
                    other_data['complications'] = st.multiselect(
                        "Complications",
                        ["None", "Medical problems", "Psychiatric symptoms", "Legal issues", "Social problems", "Other"],
                        default=other_data.get('complications', [])
                    )
            
            # DSM-5 Criteria for Other/Unknown
            if other_data.get('used'):
                st.markdown("#### DSM-5 Other Substance Use Disorder Criteria")
                other_data['dsm5_criteria'] = st.multiselect(
                    "Select all criteria that apply:",
                    DSM5_SUBSTANCE_CATEGORIES['Other_Unknown']['dsm5_criteria'],
                    default=other_data.get('dsm5_criteria', [])
                )
                
                criteria_count = len(other_data.get('dsm5_criteria', []))
                other_data['severity'] = get_dsm5_severity(criteria_count)
                st.info(f"**Severity Assessment:** {other_data['severity']} ({criteria_count} criteria)")
        
        # High-Risk Behaviors and Treatment History
        st.markdown("### ⚠️ High-Risk Behaviors and Treatment")
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if 'high_risk_behaviors' not in substance_data:
                substance_data['high_risk_behaviors'] = {}
            
            substance_data['high_risk_behaviors']['injection_use'] = st.selectbox(
                "Injection Drug Use",
                ["Never", "Past only", "Current"],
                index=0 if not substance_data['high_risk_behaviors'].get('injection_use') else ["Never", "Past only", "Current"].index(substance_data['high_risk_behaviors'].get('injection_use'))
            )
            
            substance_data['high_risk_behaviors']['needle_sharing'] = st.selectbox(
                "Needle Sharing",
                ["Never", "Past only", "Current"],
                index=0 if not substance_data['high_risk_behaviors'].get('needle_sharing') else ["Never", "Past only", "Current"].index(substance_data['high_risk_behaviors'].get('needle_sharing'))
            )
        
        with col2:
            substance_data['high_risk_behaviors']['driving_under_influence'] = st.checkbox(
                "Driving under influence",
                value=substance_data['high_risk_behaviors'].get('driving_under_influence', False)
            )
            
            substance_data['high_risk_behaviors']['risky_sexual_behavior'] = st.checkbox(
                "Risky sexual behavior while using",
                value=substance_data['high_risk_behaviors'].get('risky_sexual_behavior', False)
            )
        
        with col3:
            if 'treatment_history' not in substance_data:
                substance_data['treatment_history'] = {}
            
            substance_data['treatment_history']['previous_treatment'] = st.multiselect(
                "Previous Substance Use Treatment",
                ["None", "AA/NA meetings", "Outpatient counseling", "Intensive outpatient", "Inpatient detox", "Residential treatment", "Medication-assisted treatment", "Other"],
                default=substance_data['treatment_history'].get('previous_treatment', [])
            )
            
            substance_data['treatment_history']['treatment_response'] = st.selectbox(
                "Response to Previous Treatment",
                ["", "Excellent", "Good", "Fair", "Poor", "No response", "Not applicable"],
                index=0 if not substance_data['treatment_history'].get('treatment_response') else ["", "Excellent", "Good", "Fair", "Poor", "No response", "Not applicable"].index(substance_data['treatment_history'].get('treatment_response'))
            )
        
        # Enhanced validation with cross-section checks
        st.markdown("### 🔍 Data Validation")
        
        # Create temporary assessment data for comprehensive validation
        temp_assessment_data = {
            'substance_use': substance_data,
            'past_psychiatric_history': st.session_state.patient_data.get('past_psychiatric_history', {})
        }
        
        validation_results = perform_comprehensive_validation(temp_assessment_data)
        
        # Display validation results
        has_errors = any(errors for errors in validation_results.values())
        
        if has_errors:
            st.error("⚠️ Validation warnings detected:")
            for error_type, errors in validation_results.items():
                if errors:
                    st.error(f"**{error_type.replace('_', ' ').title()}:**")
                    for error in errors:
                        st.error(f"• {error}")
        else:
            st.success("✅ All validation checks passed")
        
        # Cross-section validation with medication history
        if 'past_psychiatric_history' in st.session_state.patient_data:
            pph = st.session_state.patient_data['past_psychiatric_history']
            
            # Check for consistency between substance use and medication history
            substance_related_meds = []
            if 'medication_history' in pph:
                for med_class, data in pph['medication_history'].items():
                    if 'trials' in data:
                        for trial in data['trials']:
                            if trial.get('medication') in ['Naltrexone', 'Acamprosate (Campral)', 'Disulfiram (Antabuse)', 'Varenicline (Chantix)']:
                                substance_related_meds.append(trial['medication'])
            
            if substance_related_meds and not any(substance_data.get('substances', {}).get(cat, {}).get('used', False) for cat in ['Alcohol', 'Tobacco', 'Other_Unknown']):
                st.warning(f"⚠️ Cross-validation warning: Substance use medications found ({', '.join(substance_related_meds)}) but no corresponding substance use documented.")
        
        st.session_state.patient_data['substance_use'] = substance_data

    # Section 8: Mental State Examination
    elif st.session_state.current_section == 8:
        st.markdown('<div class="section-header">Mental State Examination</div>', unsafe_allow_html=True)
        
        if 'mental_state_examination' not in st.session_state.patient_data:
            st.session_state.patient_data['mental_state_examination'] = {}
        
        mse = st.session_state.patient_data['mental_state_examination']
        
        # Appearance & Behavior
        st.markdown("### 👤 Appearance & Behavior")
        col1, col2 = st.columns(2)
        with col1:
            mse['appearance'] = st.multiselect(
                "Appearance",
                ['Well-groomed', 'Unkempt', 'Bizarre dress', 'Age-appropriate', 'Malnourished', 'Obese', 'Poor hygiene', 'Inappropriate dress'],
                default=mse.get('appearance', [])
            )
            
            mse['eye_contact'] = st.selectbox(
                "Eye Contact",
                ['Appropriate', 'Poor', 'Excessive', 'Avoidant'],
                index=0 if not mse.get('eye_contact') else ['Appropriate', 'Poor', 'Excessive', 'Avoidant'].index(mse.get('eye_contact'))
            )
        
        with col2:
            with st.form("appearance_behavior_form"):
                behavior = st.multiselect(
                    "Behavior",
                    ['Cooperative', 'Agitated', 'Withdrawn', 'Hostile', 'Psychomotor retardation', 'Psychomotor agitation', 'Catatonic', 'Restless', 'Guarded'],
                    default=mse.get('behavior', [])
                )
                
                behavior_submitted = st.form_submit_button("💾 Update Behavior", use_container_width=True)
                
                if behavior_submitted:
                    mse['behavior'] = behavior
                    st.success("✅ Behavior updated successfully!")
                    
                    if st.session_state.get('auto_save_enabled', True):
                        try:
                            save_assessment_data(st.session_state.patient_data)
                            st.info("💾 Auto-saved to database")
                        except Exception as e:
                            st.warning(f"Auto-save failed: {str(e)}")
            
            mse['attitude'] = st.selectbox(
                "Attitude Toward Examiner",
                ['Cooperative', 'Uncooperative', 'Hostile', 'Suspicious', 'Seductive', 'Demanding'],
                index=0 if not mse.get('attitude') else ['Cooperative', 'Uncooperative', 'Hostile', 'Suspicious', 'Seductive', 'Demanding'].index(mse.get('attitude'))
            )
        
        # Speech & Language
        st.markdown("### 🗣️ Speech & Language")
        col1, col2, col3 = st.columns(3)
        with col1:
            mse['speech_rate'] = st.selectbox(
                "Speech Rate",
                ['Normal', 'Slow', 'Rapid', 'Pressured'],
                index=0 if not mse.get('speech_rate') else ['Normal', 'Slow', 'Rapid', 'Pressured'].index(mse.get('speech_rate'))
            )
        
        with col2:
            mse['speech_volume'] = st.selectbox(
                "Speech Volume",
                ['Normal', 'Loud', 'Soft', 'Whispered'],
                index=0 if not mse.get('speech_volume') else ['Normal', 'Loud', 'Soft', 'Whispered'].index(mse.get('speech_volume'))
            )
        
        with col3:
            mse['speech_quality'] = st.multiselect(
                "Speech Quality",
                ['Clear', 'Slurred', 'Stuttering', 'Monotone', 'Dysarthric', 'Aphasia'],
                default=mse.get('speech_quality', [])
            )
        
        # Mood & Affect
        st.markdown("### 😊 Mood & Affect")
        col1, col2 = st.columns(2)
        with col1:
            mse['mood'] = st.selectbox(
                "Mood (Subjective)",
                ['Euthymic', 'Depressed', 'Elevated', 'Irritable', 'Anxious', 'Angry', 'Euphoric', 'Dysthymic', 'Mixed'],
                index=0 if not mse.get('mood') else ['Euthymic', 'Depressed', 'Elevated', 'Irritable', 'Anxious', 'Angry', 'Euphoric', 'Dysthymic', 'Mixed'].index(mse.get('mood'))
            )
            
            mse['mood_congruence'] = st.selectbox(
                "Mood Congruence",
                ['Congruent with affect', 'Incongruent with affect'],
                index=0 if not mse.get('mood_congruence') else ['Congruent with affect', 'Incongruent with affect'].index(mse.get('mood_congruence'))
            )
        
        with col2:
            mse['affect'] = st.selectbox(
                "Affect (Objective)",
                ['Appropriate', 'Inappropriate', 'Constricted', 'Blunted', 'Flat', 'Labile', 'Expansive', 'Reactive'],
                index=0 if not mse.get('affect') else ['Appropriate', 'Inappropriate', 'Constricted', 'Blunted', 'Flat', 'Labile', 'Expansive', 'Reactive'].index(mse.get('affect'))
            )
            
            mse['affect_intensity'] = st.selectbox(
                "Affect Intensity",
                ['Normal', 'Increased', 'Decreased', 'Absent'],
                index=0 if not mse.get('affect_intensity') else ['Normal', 'Increased', 'Decreased', 'Absent'].index(mse.get('affect_intensity'))
            )
        
        # Thought Process & Content
        st.markdown("### 💭 Thought Process & Content")
        col1, col2 = st.columns(2)
        with col1:
            mse['thought_process'] = st.multiselect(
                "Thought Process",
                ['Linear', 'Goal-directed', 'Tangential', 'Circumstantial', 'Flight of ideas', 'Loose associations', 'Thought blocking', 'Perseveration', 'Word salad'],
                default=mse.get('thought_process', [])
            )
            
            mse['thought_content_abnormal'] = st.multiselect(
                "Abnormal Thought Content",
                ['None', 'Delusions', 'Obsessions', 'Compulsions', 'Phobias', 'Suicidal ideation', 'Homicidal ideation', 'Paranoid thoughts'],
                default=mse.get('thought_content_abnormal', [])
            )
        
        with col2:
            if 'Delusions' in mse.get('thought_content_abnormal', []):
                mse['delusion_types'] = st.multiselect(
                    "Types of Delusions",
                    ['Persecutory', 'Grandiose', 'Somatic', 'Religious', 'Erotomanic', 'Jealous', 'Referential', 'Bizarre'],
                    default=mse.get('delusion_types', [])
                )
            
            mse['preoccupations'] = st.multiselect(
                "Preoccupations",
                ['None', 'Death', 'Guilt', 'Worthlessness', 'Somatic concerns', 'Financial worries', 'Relationship issues'],
                default=mse.get('preoccupations', [])
            )
        
        # Perceptions
        st.markdown("### 👁️ Perceptions")
        col1, col2 = st.columns(2)
        with col1:
            mse['hallucinations'] = st.multiselect(
                "Hallucinations",
                ['None', 'Auditory', 'Visual', 'Tactile', 'Olfactory', 'Gustatory', 'Command auditory'],
                default=mse.get('hallucinations', [])
            )
        
        with col2:
            mse['illusions'] = st.multiselect(
                "Illusions/Misperceptions",
                ['None', 'Visual illusions', 'Auditory illusions', 'Depersonalization', 'Derealization'],
                default=mse.get('illusions', [])
            )
        
        # Cognitive Assessment Quick Screen
        st.markdown("### 🧠 Cognitive Screen")
        col1, col2, col3 = st.columns(3)
        with col1:
            mse['orientation'] = st.selectbox(
                "Orientation",
                ['Oriented x3', 'Oriented x2', 'Oriented x1', 'Disoriented'],
                index=0 if not mse.get('orientation') else ['Oriented x3', 'Oriented x2', 'Oriented x1', 'Disoriented'].index(mse.get('orientation'))
            )
        
        with col2:
            mse['attention'] = st.selectbox(
                "Attention/Concentration",
                ['Normal', 'Mildly impaired', 'Moderately impaired', 'Severely impaired'],
                index=0 if not mse.get('attention') else ['Normal', 'Mildly impaired', 'Moderately impaired', 'Severely impaired'].index(mse.get('attention'))
            )
        
        with col3:
            mse['memory_immediate'] = st.selectbox(
                "Immediate Memory",
                ['Normal', 'Impaired'],
                index=0 if not mse.get('memory_immediate') else ['Normal', 'Impaired'].index(mse.get('memory_immediate'))
            )
        
        # Insight & Judgment
        st.markdown("### 🎯 Insight & Judgment")
        col1, col2 = st.columns(2)
        with col1:
            mse['insight'] = st.selectbox(
                "Insight",
                ['Good', 'Fair', 'Poor', 'Absent', 'Partial'],
                index=0 if not mse.get('insight') else ['Good', 'Fair', 'Poor', 'Absent', 'Partial'].index(mse.get('insight'))
            )
        
        with col2:
            mse['judgment'] = st.selectbox(
                "Judgment",
                ['Good', 'Fair', 'Poor', 'Impaired'],
                index=0 if not mse.get('judgment') else ['Good', 'Fair', 'Poor', 'Impaired'].index(mse.get('judgment'))
            )
        
        st.session_state.patient_data['mental_state_examination'] = mse

    # Section 9: Cognitive Assessment
    elif st.session_state.current_section == 9:
        st.markdown('<div class="section-header">Cognitive Assessment</div>', unsafe_allow_html=True)
        
        if 'cognitive_assessment' not in st.session_state.patient_data:
            st.session_state.patient_data['cognitive_assessment'] = {}
        
        cog = st.session_state.patient_data['cognitive_assessment']
        
        col1, col2 = st.columns(2)
        with col1:
            cog['orientation'] = st.selectbox(
                "Orientation",
                ['Oriented x3 (person, place, time)', 'Oriented x2', 'Oriented x1', 'Disoriented'],
                index=0 if not cog.get('orientation') else ['Oriented x3 (person, place, time)', 'Oriented x2', 'Oriented x1', 'Disoriented'].index(cog.get('orientation'))
            )
            
            cog['attention'] = st.selectbox(
                "Attention/Concentration",
                ['Normal', 'Mildly impaired', 'Moderately impaired', 'Severely impaired'],
                index=0 if not cog.get('attention') else ['Normal', 'Mildly impaired', 'Moderately impaired', 'Severely impaired'].index(cog.get('attention'))
            )
            
            cog['memory'] = st.selectbox(
                "Memory",
                ['Normal', 'Mildly impaired', 'Moderately impaired', 'Severely impaired'],
                index=0 if not cog.get('memory') else ['Normal', 'Mildly impaired', 'Moderately impaired', 'Severely impaired'].index(cog.get('memory'))
            )
        
        with col2:
            cog['insight'] = st.selectbox(
                "Insight",
                ['Good', 'Fair', 'Poor', 'Absent'],
                index=0 if not cog.get('insight') else ['Good', 'Fair', 'Poor', 'Absent'].index(cog.get('insight'))
            )
            
            cog['judgment'] = st.selectbox(
                "Judgment",
                ['Good', 'Fair', 'Poor', 'Impaired'],
                index=0 if not cog.get('judgment') else ['Good', 'Fair', 'Poor', 'Impaired'].index(cog.get('judgment'))
            )
            
            cog['abstract_thinking'] = st.selectbox(
                "Abstract Thinking",
                ['Normal', 'Concrete', 'Impaired'],
                index=0 if not cog.get('abstract_thinking') else ['Normal', 'Concrete', 'Impaired'].index(cog.get('abstract_thinking'))
            )
        
        st.session_state.patient_data['cognitive_assessment'] = cog

    # Section 10: Risk Assessment (with enhanced risk stratification)
    elif st.session_state.current_section == 10:
        st.markdown('<div class="section-header">Comprehensive Risk Assessment</div>', unsafe_allow_html=True)
        
        # Suicide Risk Assessment
        with st.expander("⚠️ Suicide Risk Assessment", expanded=True):
            st.markdown("**Current Suicidal Ideation:**")
            
            col1, col2 = st.columns(2)
            
            with col1:
                current_si = st.selectbox("Current Suicidal Ideation",
                                        ["None", "Passive (wish to be dead)", "Active ideation without plan",
                                         "Active ideation with plan", "Active ideation with intent"])
                
                # Conditional display based on suicidal ideation
                if current_si != "None":
                    si_frequency = st.selectbox("Frequency of Suicidal Thoughts",
                                              ["Rare", "Occasional", "Frequent", "Constant"])
                    si_intensity = st.select_slider("Intensity of Suicidal Thoughts", options=list(range(1, 11)), value=5)
                    
                    plan_details = st.text_area("Suicide Plan Details",
                                              placeholder="Method, location, timing, access to means...")
                    
                    protective_factors = st.multiselect("Protective Factors",
                                                      ["Family responsibilities", "Religious beliefs", "Future plans",
                                                       "Fear of death", "Treatment engagement", "Social support",
                                                       "Pets", "Other responsibilities"])
                else:
                    si_frequency = "Rare"
                    si_intensity = 1
                    plan_details = ""
                    protective_factors = []
            
            with col2:
                if current_si != "None":
                    means_access = st.selectbox("Access to Lethal Means",
                                              ["No access", "Limited access", "Easy access", "Immediate access"])
                    
                    deterrents = st.multiselect("What Stops Patient from Acting",
                                              ["Family", "Children", "Pets", "Religious beliefs", "Fear",
                                               "Treatment hope", "Responsibility to others"])
                    
                    si_triggers = st.text_area("Triggers for Suicidal Thoughts",
                                             placeholder="What makes suicidal thoughts worse?")
                else:
                    means_access = "No access"
                    deterrents = []
                    si_triggers = ""
            
            # Historical suicide risk factors
            st.markdown("**Historical Risk Factors:**")
            col1, col2 = st.columns(2)
            
            with col1:
                previous_attempts = st.selectbox("Previous Suicide Attempts",
                                               ["None", "1 attempt", "2-3 attempts", "> 3 attempts"])
                
                if previous_attempts != "None":
                    most_recent_attempt = st.date_input("Most Recent Attempt Date")
                    attempt_lethality = st.selectbox("Most Lethal Attempt",
                                                   ["Low lethality", "Moderate lethality", "High lethality", "Nearly fatal"])
                else:
                    most_recent_attempt = None
                    attempt_lethality = None
            
            with col2:
                family_suicide = st.selectbox("Family History of Suicide", ["No", "Yes - attempt", "Yes - completed"])
                abuse_history = st.selectbox("History of Abuse", ["None", "Physical", "Sexual", "Emotional", "Multiple types"])
            
            # Current risk level assessment
            st.markdown("**Current Suicide Risk Level:**")
            
            # Calculate risk level
            risk_data = {
                'suicide': {
                    'current_si': current_si,
                    'previous_attempts': previous_attempts,
                    'means_access': means_access if current_si != "None" else "No access",
                    'family_suicide': family_suicide
                }
            }
            
            suicide_risk_level = calculate_suicide_risk_level(risk_data)
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.selectbox("Overall Suicide Risk", 
                            ["Low", "Moderate", "High", "Imminent"],
                            index=["Low", "Moderate", "High", "Imminent"].index(suicide_risk_level),
                            disabled=True)
                
                if suicide_risk_level in ["High", "Imminent"]:
                    safety_plan_needed = st.selectbox("Safety Plan Created?", ["Yes", "No", "In progress"])
                    hospitalization_considered = st.selectbox("Hospitalization Considered?", 
                                                            ["Not needed", "Voluntary", "Involuntary", "Declined by patient"])
                else:
                    safety_plan_needed = "No"
                    hospitalization_considered = "Not needed"
            
            with col2:
                risk_factors_present = st.multiselect("Current Risk Factors Present",
                                                    ["Depression", "Hopelessness", "Isolation", "Substance use",
                                                     "Psychosis", "Impulsivity", "Recent loss", "Chronic pain",
                                                     "Financial stress", "Legal problems"])
        
        # Store risk assessment data
        risk_data = {
            'suicide': {
                'current_si': current_si, 'frequency': si_frequency if current_si != "None" else None,
                'intensity': si_intensity if current_si != "None" else None,
                'plan_details': plan_details if current_si != "None" else None,
                'means_access': means_access if current_si != "None" else None,
                'protective_factors': protective_factors if current_si != "None" else None,
                'deterrents': deterrents if current_si != "None" else None,
                'triggers': si_triggers if current_si != "None" else None,
                'previous_attempts': previous_attempts, 'family_suicide': family_suicide,
                'abuse_history': abuse_history, 'risk_level': suicide_risk_level,
                'risk_factors': risk_factors_present
            }
        }
        
        # Add conditional fields
        if previous_attempts != "None":
            risk_data['suicide']['most_recent_attempt'] = str(most_recent_attempt)
            risk_data['suicide']['attempt_lethality'] = attempt_lethality
        
        if suicide_risk_level in ["High", "Imminent"]:
            risk_data['suicide']['safety_plan'] = safety_plan_needed
            risk_data['suicide']['hospitalization'] = hospitalization_considered
        
        st.session_state.patient_data['risk_assessment'] = risk_data

    # Section 11: Laboratory & Investigations
    elif st.session_state.current_section == 11:
        st.markdown('<div class="section-header">Laboratory & Investigations</div>', unsafe_allow_html=True)
        
        if 'laboratory_investigations' not in st.session_state.patient_data:
            st.session_state.patient_data['laboratory_investigations'] = {}
        
        lab = st.session_state.patient_data['laboratory_investigations']
        
        # Complete Blood Count (CBC)
        st.markdown("### 🩸 Complete Blood Count (CBC)")
        col1, col2, col3 = st.columns(3)
        
        if 'cbc' not in lab:
            lab['cbc'] = {}
        
        with col1:
            lab['cbc']['wbc'] = st.number_input("WBC (4.0-11.0 K/μL)", 
                                               value=lab['cbc'].get('wbc') if lab['cbc'].get('wbc') is not None else None, 
                                               min_value=0.0, max_value=50.0, step=0.1)
            lab['cbc']['rbc'] = st.number_input("RBC (4.2-5.4 M/μL)", 
                                               value=lab['cbc'].get('rbc') if lab['cbc'].get('rbc') is not None else None, 
                                               min_value=0.0, max_value=10.0, step=0.1)
            lab['cbc']['hgb'] = st.number_input("Hemoglobin (12-16 g/dL)", 
                                               value=lab['cbc'].get('hgb') if lab['cbc'].get('hgb') is not None else None, 
                                               min_value=0.0, max_value=25.0, step=0.1)
        
        with col2:
            lab['cbc']['hct'] = st.number_input("Hematocrit (36-46%)", 
                                               value=lab['cbc'].get('hct') if lab['cbc'].get('hct') is not None else None, 
                                               min_value=0.0, max_value=100.0, step=0.1)
            lab['cbc']['plt'] = st.number_input("Platelets (150-450 K/μL)", 
                                               value=lab['cbc'].get('plt') if lab['cbc'].get('plt') is not None else None, 
                                               min_value=0.0, max_value=1000.0, step=1.0)
            lab['cbc']['mcv'] = st.number_input("MCV (80-100 fL)", 
                                               value=lab['cbc'].get('mcv') if lab['cbc'].get('mcv') is not None else None, 
                                               min_value=0.0, max_value=150.0, step=0.1)
        
        with col3:
            lab['cbc']['neutrophils'] = st.number_input("Neutrophils (%)", 
                                                       value=lab['cbc'].get('neutrophils') if lab['cbc'].get('neutrophils') is not None else 0.0, 
                                                       min_value=0.0, max_value=100.0, step=0.1)
            lab['cbc']['lymphocytes'] = st.number_input("Lymphocytes (%)", 
                                                       value=lab['cbc'].get('lymphocytes') if lab['cbc'].get('lymphocytes') is not None else 0.0, 
                                                       min_value=0.0, max_value=100.0, step=0.1)
            lab['cbc']['monocytes'] = st.number_input("Monocytes (%)", 
                                                     value=lab['cbc'].get('monocytes') if lab['cbc'].get('monocytes') is not None else 0.0, 
                                                     min_value=0.0, max_value=100.0, step=0.1)
        
        # Basic Metabolic Panel (BMP)
        st.markdown("### ⚗️ Basic Metabolic Panel (BMP)")
        col1, col2, col3 = st.columns(3)
        
        if 'bmp' not in lab:
            lab['bmp'] = {}
        
        with col1:
            lab['bmp']['glucose'] = st.number_input("Glucose (70-100 mg/dL)", 
                                                   value=lab['bmp'].get('glucose') if lab['bmp'].get('glucose') is not None else None, 
                                                   min_value=0.0, max_value=500.0, step=1.0)
            lab['bmp']['bun'] = st.number_input("BUN (7-20 mg/dL)", 
                                               value=lab['bmp'].get('bun') if lab['bmp'].get('bun') is not None else None, 
                                               min_value=0.0, max_value=200.0, step=1.0)
            lab['bmp']['creatinine'] = st.number_input("Creatinine (0.6-1.2 mg/dL)", 
                                                      value=lab['bmp'].get('creatinine') if lab['bmp'].get('creatinine') is not None else None, 
                                                      min_value=0.0, max_value=20.0, step=0.1)
        
        with col2:
            lab['bmp']['sodium'] = st.number_input("Sodium (136-145 mEq/L)", 
                                                  value=lab['bmp'].get('sodium') if lab['bmp'].get('sodium') is not None else None, 
                                                  min_value=0.0, max_value=200.0, step=1.0)
            lab['bmp']['potassium'] = st.number_input("Potassium (3.5-5.0 mEq/L)", 
                                                     value=lab['bmp'].get('potassium') if lab['bmp'].get('potassium') is not None else None, 
                                                     min_value=0.0, max_value=10.0, step=0.1)
            lab['bmp']['chloride'] = st.number_input("Chloride (98-107 mEq/L)", 
                                                    value=lab['bmp'].get('chloride') if lab['bmp'].get('chloride') is not None else None, 
                                                    min_value=0.0, max_value=200.0, step=1.0)
        
        with col3:
            lab['bmp']['co2'] = st.number_input("CO2 (22-28 mEq/L)", 
                                               value=lab['bmp'].get('co2') if lab['bmp'].get('co2') is not None else None, 
                                               min_value=0.0, max_value=50.0, step=1.0)
            lab['bmp']['egfr'] = st.number_input("eGFR (>60 mL/min/1.73m²)", 
                                                value=lab['bmp'].get('egfr') if lab['bmp'].get('egfr') is not None else None, 
                                                min_value=0.0, max_value=200.0, step=1.0)
        
        # Liver Function Tests
        st.markdown("### 🫀 Liver Function Tests")
        col1, col2 = st.columns(2)
        
        if 'lft' not in lab:
            lab['lft'] = {}
        
        with col1:
            lab['lft']['alt'] = st.number_input("ALT (7-56 U/L)", 
                                               value=lab['lft'].get('alt') if lab['lft'].get('alt') is not None else None, 
                                               min_value=0.0, max_value=1000.0, step=1.0)
            lab['lft']['ast'] = st.number_input("AST (10-40 U/L)", 
                                               value=lab['lft'].get('ast') if lab['lft'].get('ast') is not None else None, 
                                               min_value=0.0, max_value=1000.0, step=1.0)
            lab['lft']['bilirubin_total'] = st.number_input("Total Bilirubin (0.3-1.2 mg/dL)", 
                                                           value=lab['lft'].get('bilirubin_total') if lab['lft'].get('bilirubin_total') is not None else None, 
                                                           min_value=0.0, max_value=50.0, step=0.1)
        
        with col2:
            lab['lft']['alkaline_phosphatase'] = st.number_input("Alkaline Phosphatase (44-147 U/L)", 
                                                               value=lab['lft'].get('alkaline_phosphatase') if lab['lft'].get('alkaline_phosphatase') is not None else None, 
                                                               min_value=0.0, max_value=1000.0, step=1.0)
            lab['lft']['albumin'] = st.number_input("Albumin (3.5-5.0 g/dL)", 
                                                   value=lab['lft'].get('albumin') if lab['lft'].get('albumin') is not None else None, 
                                                   min_value=0.0, max_value=10.0, step=0.1)
            lab['lft']['protein_total'] = st.number_input("Total Protein (6.0-8.3 g/dL)", 
                                                         value=lab['lft'].get('protein_total') if lab['lft'].get('protein_total') is not None else None, 
                                                         min_value=0.0, max_value=15.0, step=0.1)
        
        # Thyroid Function
        st.markdown("### 🦋 Thyroid Function")
        col1, col2 = st.columns(2)
        
        if 'thyroid' not in lab:
            lab['thyroid'] = {}
        
        with col1:
            lab['thyroid']['tsh'] = st.number_input("TSH (0.4-4.0 mIU/L)", 
                                                   value=lab['thyroid'].get('tsh') if lab['thyroid'].get('tsh') is not None else None, 
                                                   min_value=0.0, max_value=100.0, step=0.1)
            lab['thyroid']['t4_free'] = st.number_input("Free T4 (0.8-1.8 ng/dL)", 
                                                       value=lab['thyroid'].get('t4_free') if lab['thyroid'].get('t4_free') is not None else None, 
                                                       min_value=0.0, max_value=10.0, step=0.1)
        
        with col2:
            lab['thyroid']['t3_free'] = st.number_input("Free T3 (2.3-4.2 pg/mL)", 
                                                       value=lab['thyroid'].get('t3_free') if lab['thyroid'].get('t3_free') is not None else None, 
                                                       min_value=0.0, max_value=20.0, step=0.1)
        
        # Vitamins & Other
        st.markdown("### 💊 Vitamins & Other Tests")
        col1, col2 = st.columns(2)
        
        if 'vitamins' not in lab:
            lab['vitamins'] = {}
        
        with col1:
            lab['vitamins']['b12'] = st.number_input("Vitamin B12 (200-900 pg/mL)", 
                                                    value=lab['vitamins'].get('b12') if lab['vitamins'].get('b12') is not None else None, 
                                                    min_value=0.0, max_value=2000.0, step=10.0)
            lab['vitamins']['folate'] = st.number_input("Folate (>3.0 ng/mL)", 
                                                       value=lab['vitamins'].get('folate') if lab['vitamins'].get('folate') is not None else None, 
                                                       min_value=0.0, max_value=50.0, step=0.1)
            lab['vitamins']['vitamin_d'] = st.number_input("Vitamin D (30-100 ng/mL)", 
                                                          value=lab['vitamins'].get('vitamin_d') if lab['vitamins'].get('vitamin_d') is not None else None, 
                                                          min_value=0.0, max_value=200.0, step=1.0)
        
        with col2:
            lab['vitamins']['hba1c'] = st.number_input("HbA1c (%)", 
                                                      value=lab['vitamins'].get('hba1c') if lab['vitamins'].get('hba1c') is not None else None, 
                                                      min_value=0.0, max_value=20.0, step=0.1)
            lab['vitamins']['magnesium'] = st.number_input("Magnesium (1.7-2.2 mg/dL)", 
                                                          value=lab['vitamins'].get('magnesium') if lab['vitamins'].get('magnesium') is not None else None, 
                                                          min_value=0.0, max_value=10.0, step=0.1)
        
        # Imaging Studies
        st.markdown("### 🔬 Imaging Studies")
        if 'imaging' not in lab:
            lab['imaging'] = {}
        
        lab['imaging']['studies_ordered'] = st.multiselect(
            "Imaging Studies Ordered",
            ["CT Head", "MRI Brain", "Chest X-ray", "CT Chest", "CT Abdomen/Pelvis", "Ultrasound", "PET Scan", "SPECT", "Other"],
            default=lab['imaging'].get('studies_ordered', [])
        )
        
        if lab['imaging']['studies_ordered']:
            lab['imaging']['results'] = st.text_area(
                "Imaging Results Summary",
                value=lab['imaging'].get('results', ''),
                height=100
            )
        
        # Other Tests
        st.markdown("### 🧪 Other Specialized Tests")
        if 'other_tests' not in lab:
            lab['other_tests'] = {}
        
        lab['other_tests']['tests_ordered'] = st.multiselect(
            "Other Tests Ordered",
            ["EEG", "Neuropsychological Testing", "Drug Screen", "Alcohol Level", "Lithium Level", "Valproic Acid Level", "Carbamazepine Level", "Other Medication Levels"],
            default=lab['other_tests'].get('tests_ordered', [])
        )
        
        if lab['other_tests']['tests_ordered']:
            lab['other_tests']['results'] = st.text_area(
                "Other Test Results",
                value=lab['other_tests'].get('results', ''),
                height=80
            )
        
        st.session_state.patient_data['laboratory_investigations'] = lab

    # Section 12: Clinical Scales & Ratings
    elif st.session_state.current_section == 12:
        st.markdown('<div class="section-header">Clinical Scales & Ratings</div>', unsafe_allow_html=True)
        
        if 'clinical_scales' not in st.session_state.patient_data:
            st.session_state.patient_data['clinical_scales'] = {}
        
        scales = st.session_state.patient_data['clinical_scales']
        
        # PHQ-9 Depression Scale
        st.markdown("### PHQ-9 Depression Scale")
        if 'depression' not in scales:
            scales['depression'] = {}
        
        phq9_questions = [
            "Little interest or pleasure in doing things",
            "Feeling down, depressed, or hopeless", 
            "Trouble falling or staying asleep, or sleeping too much",
            "Feeling tired or having little energy",
            "Poor appetite or overeating",
            "Feeling bad about yourself or that you are a failure",
            "Trouble concentrating on things",
            "Moving or speaking slowly or being fidgety/restless",
            "Thoughts that you would be better off dead or hurting yourself"
        ]
        
        phq9_total = 0
        for i, question in enumerate(phq9_questions):
            score = st.selectbox(
                f"PHQ-9 Q{i+1}: {question}",
                ["Not at all (0)", "Several days (1)", "More than half the days (2)", "Nearly every day (3)"],
                index=scales['depression'].get(f'q{i+1}', 0),
                key=f"phq9_q{i+1}"
            )
            try:
                score_value = int(score.split('(')[1].split(')')[0])
                # Validate score range
                if 0 <= score_value <= 3:
                    scales['depression'][f'q{i+1}'] = score_value
                    phq9_total += score_value
                else:
                    st.error(f"Invalid PHQ-9 score: {score_value}. Must be between 0 and 3.")
                    scales['depression'][f'q{i+1}'] = 0
            except (ValueError, IndexError) as e:
                st.error(f"Error parsing PHQ-9 score for question {i+1}: {e}")
                scales['depression'][f'q{i+1}'] = 0

        # Validate total score
        if 0 <= phq9_total <= 27:
            scales['depression']['phq9_total'] = phq9_total
            st.info(f"PHQ-9 Total Score: {phq9_total}/27")
        else:
            st.error(f"Invalid PHQ-9 total score: {phq9_total}. Must be between 0 and 27.")
            scales['depression']['phq9_total'] = 0
        
        # GAD-7 Anxiety Scale
        st.markdown("### GAD-7 Anxiety Scale")
        if 'anxiety' not in scales:
            scales['anxiety'] = {}
        
        gad7_questions = [
            "Feeling nervous, anxious, or on edge",
            "Not being able to stop or control worrying",
            "Worrying too much about different things", 
            "Trouble relaxing",
            "Being so restless that it is hard to sit still",
            "Becoming easily annoyed or irritable",
            "Feeling afraid, as if something awful might happen"
        ]
        
        gad7_total = 0
        for i, question in enumerate(gad7_questions):
            score = st.selectbox(
                f"GAD-7 Q{i+1}: {question}",
                ["Not at all (0)", "Several days (1)", "More than half the days (2)", "Nearly every day (3)"],
                index=scales['anxiety'].get(f'q{i+1}', 0),
                key=f"gad7_q{i+1}"
            )
            try:
                score_value = int(score.split('(')[1].split(')')[0])
                # Validate score range
                if 0 <= score_value <= 3:
                    scales['anxiety'][f'q{i+1}'] = score_value
                    gad7_total += score_value
                else:
                    st.error(f"Invalid GAD-7 score: {score_value}. Must be between 0 and 3.")
                    scales['anxiety'][f'q{i+1}'] = 0
            except (ValueError, IndexError) as e:
                st.error(f"Error parsing GAD-7 score for question {i+1}: {e}")
                scales['anxiety'][f'q{i+1}'] = 0

        # Validate total score
        if 0 <= gad7_total <= 21:
            scales['anxiety']['gad7_total'] = gad7_total
            st.info(f"GAD-7 Total Score: {gad7_total}/21")
        else:
            st.error(f"Invalid GAD-7 total score: {gad7_total}. Must be between 0 and 21.")
            scales['anxiety']['gad7_total'] = 0
        
        st.session_state.patient_data['clinical_scales'] = scales

    # Section 13: Diagnostic Formulation (with templated text)
    elif st.session_state.current_section == 13:
        st.markdown('<div class="section-header">Diagnostic Formulation</div>', unsafe_allow_html=True)
        
        # Primary diagnoses
        st.markdown('<div class="subsection-header">Primary Psychiatric Diagnoses</div>', unsafe_allow_html=True)
        
        col1, col2 = st.columns(2)
        
        with col1:
            primary_diagnosis = st.selectbox("Primary Diagnosis",
                                           ["Major Depressive Disorder", "Bipolar I Disorder", "Bipolar II Disorder",
                                            "Generalized Anxiety Disorder", "Panic Disorder", "Social Anxiety Disorder",
                                            "PTSD", "OCD", "ADHD", "Schizophrenia", "Schizoaffective Disorder",
                                            "Brief Psychotic Disorder", "Borderline Personality Disorder", 
                                            "Substance Use Disorder", "Adjustment Disorder", "Other", "Deferred"])
            
            primary_specifiers = st.multiselect("Primary Diagnosis Specifiers",
                                              ["Single episode", "Recurrent", "With psychotic features", "Severe",
                                               "With mixed features", "With rapid cycling", "Current episode manic",
                                               "Current episode depressed", "In remission", "Mild", "Moderate"])
            
            confidence_primary = st.select_slider("Diagnostic Confidence - Primary", 
                                                options=["Low", "Moderate", "High", "Very High"])
        
        with col2:
            secondary_diagnoses = st.multiselect("Secondary/Comorbid Diagnoses",
                                               ["Major Depressive Disorder", "Generalized Anxiety Disorder", "PTSD",
                                                "Substance Use Disorder", "Personality Disorder", "ADHD", "OCD",
                                                "Eating Disorder", "Sleep Disorder", "Other"])
            
            rule_out_diagnoses = st.multiselect("Rule Out/Differential Diagnoses",
                                              ["Bipolar Disorder", "Psychotic Disorder", "Personality Disorder",
                                               "Medical condition", "Substance-induced", "Adjustment Disorder"])
            
            diagnostic_certainty = st.selectbox("Overall Diagnostic Certainty",
                                              ["Provisional", "Working diagnosis", "Confident", "Definitive"])
        
        # Biopsychosocial formulation
        st.markdown('<div class="subsection-header">Biopsychosocial Formulation</div>', unsafe_allow_html=True)
        
        # Template buttons
        st.markdown("**Quick Templates:**")
        template_cols = st.columns(5)
        with template_cols[0]:
            if st.button("Depression", key="depression_template"):
                insert_template("depression", "biological_factors")
        with template_cols[1]:
            if st.button("Anxiety", key="anxiety_template"):
                insert_template("anxiety", "psychological_factors")
        with template_cols[2]:
            if st.button("Psychosis", key="psychosis_template"):
                insert_template("psychosis", "social_factors")
        with template_cols[3]:
            if st.button("Substance", key="substance_template"):
                insert_template("substance", "precipitating_factors")
        with template_cols[4]:
            if st.button("Trauma", key="trauma_template"):
                insert_template("trauma", "case_summary")
        
        col1, col2 = st.columns(2)
        
        with col1:
            biological_factors = st.text_area("Biological Factors",
                                            placeholder="Genetic predisposition, medical conditions, medication effects, substance use...",
                                            height=120, key="biological_factors")
            
            psychological_factors = st.text_area("Psychological Factors", 
                                               placeholder="Personality traits, coping mechanisms, cognitive patterns, trauma history...",
                                               height=120, key="psychological_factors")
        
        with col2:
            social_factors = st.text_area("Social Factors",
                                        placeholder="Family dynamics, social support, cultural factors, socioeconomic status...",
                                        height=120, key="social_factors")
            
            precipitating_factors = st.text_area("Precipitating Factors",
                                               placeholder="Recent events that triggered current episode...",
                                               height=120, key="precipitating_factors")
        
        # Case formulation summary
        st.markdown('<div class="subsection-header">Case Formulation Summary</div>', unsafe_allow_html=True)
        
        case_summary = st.text_area("Integrated Case Formulation",
                                   placeholder="Provide a comprehensive summary integrating all biopsychosocial factors...",
                                   height=200, key="case_summary")
        
        # Store diagnostic formulation data
        diagnostic_data = {
            'diagnoses': {
                'primary': primary_diagnosis, 'primary_specifiers': primary_specifiers,
                'confidence_primary': confidence_primary, 'secondary': secondary_diagnoses,
                'rule_out': rule_out_diagnoses, 'certainty': diagnostic_certainty
            },
            'biopsychosocial': {
                'biological': biological_factors, 'psychological': psychological_factors,
                'social': social_factors, 'precipitating': precipitating_factors
            },
            'case_summary': case_summary
        }
        
        st.session_state.patient_data['diagnostic_formulation'] = diagnostic_data

    # Section 14: Treatment Planning
    elif st.session_state.current_section == 14:
        st.markdown('<div class="section-header">Treatment Planning</div>', unsafe_allow_html=True)
        
        if 'treatment_planning' not in st.session_state.patient_data:
            st.session_state.patient_data['treatment_planning'] = {}
        
        treatment = st.session_state.patient_data['treatment_planning']
        
        col1, col2 = st.columns(2)
        with col1:
            treatment['immediate_interventions'] = st.text_area(
                "Immediate Interventions",
                value=treatment.get('immediate_interventions', ''),
                height=100,
                help="Crisis interventions, safety planning, etc."
            )
            
            treatment['medication_recommendations'] = st.text_area(
                "Medication Recommendations",
                value=treatment.get('medication_recommendations', ''),
                height=100
            )
            
            treatment['therapy_recommendations'] = st.text_area(
                "Therapy Recommendations",
                value=treatment.get('therapy_recommendations', ''),
                height=100,
                help="CBT, DBT, psychodynamic, etc."
            )
        
        with col2:
            treatment['goals'] = st.text_area(
                "Treatment Goals",
                value=treatment.get('goals', ''),
                height=100,
                help="Short-term and long-term goals"
            )
            
            treatment['referrals'] = st.text_area(
                "Referrals",
                value=treatment.get('referrals', ''),
                height=100,
                help="Specialists, programs, services"
            )
            
            treatment['patient_preferences'] = st.text_area(
                "Patient Preferences",
                value=treatment.get('patient_preferences', ''),
                height=100
            )
        
        st.session_state.patient_data['treatment_planning'] = treatment

    # Section 15: Follow-up & Monitoring
    elif st.session_state.current_section == 15:
        st.markdown('<div class="section-header">Follow-up & Monitoring</div>', unsafe_allow_html=True)
        
        if 'follow_up_monitoring' not in st.session_state.patient_data:
            st.session_state.patient_data['follow_up_monitoring'] = {}
        
        followup = st.session_state.patient_data['follow_up_monitoring']
        
        col1, col2 = st.columns(2)
        with col1:
            followup['next_appointment'] = st.date_input(
                "Next Appointment Date",
                value=followup.get('next_appointment', datetime.date.today() + datetime.timedelta(days=14))
            )
            
            followup['frequency'] = st.selectbox(
                "Follow-up Frequency",
                ["Weekly", "Bi-weekly", "Monthly", "As needed", "Other"],
                index=0 if not followup.get('frequency') else ["Weekly", "Bi-weekly", "Monthly", "As needed", "Other"].index(followup.get('frequency'))
            )
            
            followup['monitoring_parameters'] = st.text_area(
                "Monitoring Parameters",
                value=followup.get('monitoring_parameters', ''),
                height=100,
                help="What to monitor (symptoms, side effects, etc.)"
            )
        
        with col2:
            followup['warning_signs'] = st.text_area(
                "Warning Signs to Watch For",
                value=followup.get('warning_signs', ''),
                height=100
            )
            
            followup['emergency_contacts'] = st.text_area(
                "Emergency Contacts",
                value=followup.get('emergency_contacts', ''),
                height=100,
                help="Crisis hotlines, emergency contacts"
            )
            
            followup['patient_education'] = st.text_area(
                "Patient Education Provided",
                value=followup.get('patient_education', ''),
                height=100
            )
        
        st.session_state.patient_data['follow_up_monitoring'] = followup

    # Navigation and action buttons (with improved reset functionality)
    st.markdown("---")
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        if st.session_state.current_section > 0:
            if st.button("⬅️ Previous Section"):
                st.session_state.current_section -= 1
                st.rerun()
        else:
            st.button("⬅️ Previous Section", disabled=True, help="Already at first section")
    with col2:
        # Validate before allowing save
        can_save = st.session_state.patient_id and st.session_state.patient_data
        save_help = "Save current progress" if can_save else "Enter patient code first"

        if st.button("💾 Save Progress", disabled=not can_save, help=save_help):
            if save_current_assessment():
                st.balloons()  # Celebration for successful save
    with col3:
        # Show Next button only if not on the final section
        if st.session_state.current_section < 15:  # 15 is the last section index (0-based)
            # Simple validation - just need patient ID for navigation
            can_proceed = bool(st.session_state.patient_id)
            next_help = "Go to next section" if can_proceed else "Enter patient ID first"

            if st.button("➡️ Next Section", disabled=not can_proceed, help=next_help):
                st.session_state.current_section += 1
                st.rerun()
        else:
            # On final section, show Complete Assessment button
            can_complete = st.session_state.patient_id and st.session_state.patient_data
            complete_help = "Complete and save assessment" if can_complete else "Complete all sections first"

            if st.button("✅ Complete Assessment", disabled=not can_complete, help=complete_help):
                if save_current_assessment():
                    st.success("🎉 Assessment completed successfully!")
                    st.balloons()  # Celebration for completion
                    st.session_state.current_view = 'dashboard'
                    st.rerun()
    with col4:
        # Improved reset functionality
        if st.button("🔄 Reset Assessment"):
            st.session_state.show_reset_confirm = True

    # Reset confirmation dialog
    if st.session_state.get('show_reset_confirm', False):
        st.warning("⚠️ Are you sure you want to reset the current assessment? All unsaved data will be lost.")
        col1, col2 = st.columns(2)
        with col1:
            if st.button("Yes, Reset"):
                st.session_state.patient_data = {}
                st.session_state.current_section = 0
                st.session_state.patient_id = None  # Clear patient code
                st.session_state.editing_assessment_id = None
                st.session_state.show_reset_confirm = False
                st.session_state.patient_code_entered = False
                st.rerun()
        with col2:
            if st.button("Cancel"):
                st.session_state.show_reset_confirm = False
                st.rerun()

    # Auto-save functionality
    auto_save_progress()

def show_dashboard_view():
    """Display the assessment dashboard"""
    st.markdown('<h1 class="main-header">📊 Psychiatric Assessment Dashboard</h1>', unsafe_allow_html=True)

    # Show current assessment status
    if st.session_state.get('patient_data') and st.session_state.get('patient_id'):
        st.info(f"📝 **Assessment in Progress:** Patient {st.session_state.patient_id} - Section {st.session_state.current_section + 1}/16")

    # Dashboard navigation
    col1, col2, col3, col4, col5 = st.columns(5)
    with col1:
        if st.button("🧠 New Assessment"):
            new_assessment()
    with col2:
        # Check if there's an assessment in progress
        if st.session_state.get('patient_data') and st.session_state.get('patient_id'):
            if st.button("📝 Continue Assessment"):
                st.session_state.current_view = 'assessment'
                st.rerun()
        else:
            if st.button("📝 Back to Assessment"):
                st.session_state.current_view = 'assessment'
                st.rerun()
    with col3:
        if st.button("🔄 Refresh Data"):
            # Use session state to trigger refresh without immediate rerun
            st.session_state.refresh_requested = True
    with col4:
        if st.button("📋 Validate Database"):
            valid, message = validate_database()
            if valid:
                st.success(message)
            else:
                st.error(message)
            
            # Also run comprehensive validation on current session
            st.markdown("### 🔍 Session Data Validation")
            if 'assessment_data' in st.session_state and st.session_state.assessment_data:
                validation_results = perform_comprehensive_validation(st.session_state.assessment_data)
                
                total_errors = sum(len(errors) for errors in validation_results.values())
                if total_errors > 0:
                    st.warning(f"Found {total_errors} validation issues in current session:")
                    for error_type, errors in validation_results.items():
                        if errors:
                            st.error(f"**{error_type.replace('_', ' ').title()}:** {len(errors)} issues")
                            with st.expander(f"View {error_type} details"):
                                for error in errors:
                                    st.write(f"• {error}")
                else:
                    st.success("✅ Current session data passes all validation checks")
            else:
                st.info("No assessment data in current session to validate")
    with col5:
        if st.button("📥 Export ML Dataset"):
            ml_data = get_ml_dataset()
            if not ml_data.empty:
                csv = ml_data.to_csv(index=False)
                st.download_button(
                    label="Download ML Dataset",
                    data=csv,
                    file_name=f"psychiatric_ml_dataset_{datetime.datetime.now().strftime('%Y%m%d')}.csv",
                    mime="text/csv"
                )
            else:
                st.warning("No data available for export")
    
    # Search and filter
    st.markdown("### 🔍 Search and Filter Assessments")
    
    col1, col2, col3 = st.columns(3)
    with col1:
        search_term = st.text_input("Search by Patient ID", placeholder="Enter patient ID...")
    with col2:
        diagnosis_filter = st.multiselect("Filter by Diagnosis", 
                                         ["Major Depressive Disorder", "Bipolar I Disorder", "Bipolar II Disorder",
                                          "Generalized Anxiety Disorder", "PTSD", "OCD", "ADHD", "Schizophrenia",
                                          "Substance Use Disorder", "Other"],
                                         key="diagnosis_filter_multiselect")
    with col3:
        risk_filter = st.multiselect("Filter by Risk Level", ["Low", "Moderate", "High", "Imminent"],
                                   key="risk_filter_multiselect")
    
    # Handle refresh request after filters are processed
    if st.session_state.get('refresh_requested', False):
        st.session_state.refresh_requested = False
        st.rerun()
    
    # Get all patients
    patients_df = get_all_patients()
    
    if not patients_df.empty:
        # Apply filters
        if search_term:
            patients_df = patients_df[patients_df['patient_id'].str.contains(search_term, case=False, na=False)]
        
        # Get assessments for filtered patients
        if not patients_df.empty:
            patient_ids = patients_df['patient_id'].tolist()
            all_assessments = []
            
            for patient_id in patient_ids:
                assessments = get_patient_assessments(patient_id)
                if not assessments.empty:
                    all_assessments.append(assessments)
            
            if all_assessments:
                assessments_df = pd.concat(all_assessments)
                
                # Apply additional filters
                if diagnosis_filter:
                    assessments_df = assessments_df[assessments_df['primary_diagnosis'].isin(diagnosis_filter)]
                
                if risk_filter:
                    assessments_df = assessments_df[assessments_df['suicide_risk_level'].isin(risk_filter)]
                
                # Display metrics
                st.markdown("### 📈 Assessment Metrics")
                col1, col2, col3, col4 = st.columns(4)
                
                with col1:
                    st.metric("Total Patients", len(patients_df))
                with col2:
                    st.metric("Total Assessments", len(assessments_df))
                with col3:
                    avg_completion = assessments_df['completion_percentage'].mean() if not assessments_df.empty else 0
                    st.metric("Avg. Completion", f"{avg_completion:.1f}%")
                with col4:
                    high_risk_count = len(assessments_df[assessments_df['suicide_risk_level'].isin(['High', 'Imminent'])])
                    st.metric("High Risk Cases", high_risk_count)
                
                # Display charts
                st.markdown("### 📊 Data Visualization")
                
                col1, col2 = st.columns(2)
                
                with col1:
                    # Diagnosis distribution
                    if not assessments_df.empty:
                        diagnosis_counts = assessments_df['primary_diagnosis'].value_counts().reset_index()
                        diagnosis_counts.columns = ['Diagnosis', 'Count']
                        
                        fig_diagnosis = px.pie(
                            diagnosis_counts, 
                            values='Count', 
                            names='Diagnosis',
                            title="Primary Diagnosis Distribution",
                            hole=0.3
                        )
                        fig_diagnosis.update_traces(textposition='inside', textinfo='percent+label')
                        st.plotly_chart(fig_diagnosis, use_container_width=True)
                
                with col2:
                    # Risk level distribution
                    if not assessments_df.empty:
                        risk_counts = assessments_df['suicide_risk_level'].value_counts().reset_index()
                        risk_counts.columns = ['Risk Level', 'Count']
                        
                        # Order risk levels
                        risk_order = ['Low', 'Moderate', 'High', 'Imminent']
                        risk_counts['Risk Level'] = pd.Categorical(risk_counts['Risk Level'], categories=risk_order, ordered=True)
                        risk_counts = risk_counts.sort_values('Risk Level')
                        
                        fig_risk = px.bar(
                            risk_counts,
                            x='Risk Level',
                            y='Count',
                            title="Suicide Risk Level Distribution",
                            color='Risk Level',
                            color_discrete_map={
                                'Low': '#10b981',
                                'Moderate': '#f59e0b',
                                'High': '#ef4444',
                                'Imminent': '#dc2626'
                            }
                        )
                        st.plotly_chart(fig_risk, use_container_width=True)
                
                # Display patient assessments
                st.markdown("### 📋 Patient Assessments")
                
                # Group by patient
                assessments_df['assessment_date'] = pd.to_datetime(assessments_df['assessment_date'])
                assessments_df = assessments_df.sort_values('assessment_date', ascending=False)
                
                for _, row in assessments_df.iterrows():
                    patient_id = row['patient_id']
                    patient_data = patients_df[patients_df['patient_id'] == patient_id].iloc[0] if not patients_df[patients_df['patient_id'] == patient_id].empty else None
                    
                    if patient_data is not None:
                        with st.expander(f"📝 {patient_id} - {row['assessment_date'].strftime('%Y-%m-%d %H:%M')}"):
                            col1, col2, col3 = st.columns(3)
                            
                            with col1:
                                st.markdown(f"**Age:** {patient_data['age'] if pd.notna(patient_data['age']) else 'Not specified'}")
                                st.markdown(f"**Gender:** {patient_data['gender']}")
                                st.markdown(f"**Primary Diagnosis:** {row['primary_diagnosis'] if pd.notna(row['primary_diagnosis']) else 'Not specified'}")
                            
                            with col2:
                                risk_level = row['suicide_risk_level'] if pd.notna(row['suicide_risk_level']) else 'Unknown'
                                risk_class = risk_level.lower() if risk_level and risk_level != 'Unknown' else 'unknown'
                                st.markdown(f"**Risk Level:** <span class='risk-{risk_class}'>{risk_level}</span>", unsafe_allow_html=True)
                                if pd.notna(row['phq9_score']):
                                    st.markdown(f"**PHQ-9 Score:** {row['phq9_score']}")
                                if pd.notna(row['gad7_score']):
                                    st.markdown(f"**GAD-7 Score:** {row['gad7_score']}")
                            
                            with col3:
                                st.markdown(f"**Completion:** {row['completion_percentage']:.1f}%")
                                st.markdown(f"**Duration:** {row['duration_minutes']:.1f} minutes")
                                
                                # Action buttons
                                col1, col2 = st.columns(2)
                                with col1:
                                    if st.button("Edit", key=f"edit_{row['assessment_id']}"):
                                        load_assessment(row['assessment_id'])
                                with col2:
                                    if st.button("Delete", key=f"delete_{row['assessment_id']}"):
                                        # Show confirmation dialog
                                        if f"confirm_delete_{row['assessment_id']}" not in st.session_state:
                                            st.session_state[f"confirm_delete_{row['assessment_id']}"] = False
                                        
                                        if not st.session_state[f"confirm_delete_{row['assessment_id']}"]:
                                            st.session_state[f"confirm_delete_{row['assessment_id']}"] = True
                                            st.rerun()
                                        else:
                                            # Show confirmation dialog
                                            st.warning(f"⚠️ Are you sure you want to delete assessment {row['assessment_id']}? This action cannot be undone.")
                                            col1_confirm, col2_confirm = st.columns(2)
                                            with col1_confirm:
                                                if st.button("Yes, Delete", key=f"yes_delete_{row['assessment_id']}"):
                                                    if delete_assessment(row['assessment_id']):
                                                        st.success("Assessment deleted successfully")
                                                        # Clear confirmation state
                                                        st.session_state[f"confirm_delete_{row['assessment_id']}"] = False
                                                        st.rerun()
                                                    else:
                                                        st.error("Failed to delete assessment")
                                            with col2_confirm:
                                                if st.button("Cancel", key=f"cancel_delete_{row['assessment_id']}"):
                                                    st.session_state[f"confirm_delete_{row['assessment_id']}"] = False
                                                    st.rerun()
            else:
                st.warning("No assessments found for the filtered patients.")
        else:
            st.warning("No patients found matching your search criteria.")
    else:
        st.warning("No patient data available. Please create an assessment first.")

# Main app entry point
if __name__ == "__main__":
    main()